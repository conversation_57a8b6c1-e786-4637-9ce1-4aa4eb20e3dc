#!/usr/bin/env python3
"""
Fix the column order in MAT_2025.csv to match the SQL Server table structure
"""

import pandas as pd

def fix_mat_column_order():
    """Fix the column order in MAT CSV to match SQL table"""
    
    print("🔧 FIXING MAT COLUMN ORDER")
    print("=" * 50)
    
    try:
        # Read the current CSV
        print("📖 Reading MAT_2025.csv...")
        df = pd.read_csv('MAT_2025.csv', sep=';')
        print(f"✓ Loaded {len(df)} records")
        
        print("\n📋 Current column order:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # Define the correct column order based on your sample data
        correct_order = [
            'ID', 'TIPO_DOC', 'N_DOC', 'DV', 'PRIMER_APELLIDO', 'SEGUNDO_APELLIDO', 
            'NOMBRE', 'SEXO', 'FECH_NAC', 'NAC', 'FOR_ING_ACT', 'ANIO_ING_ACT', 
            'SEM_ING_ACT', 'ANIO_ING_ORI', 'SEM_ING_ORI', 'VIG', 'AÑO_PROCESO', 
            'COD_SIES', 'NOMBRE_CARRERA', 'FACULTAD', 'LLAVE_SIES', 'COHORTE_ESTUDIANTE',
            'LLAVE_RUT_AÑO', 'LLAVE_RUT_COHORTE', 'TIPO_PLAN', 'NIVEL', 
            'ÁREA DEL CONOCIMIENTO', 'CLASIFICACION_NIVEL_1', 'CLASIFICACION_NIVEL_2'
        ]
        
        print("\n📋 Target column order:")
        for i, col in enumerate(correct_order, 1):
            print(f"{i:2d}. {col}")
        
        # Check if all columns exist
        missing_cols = [col for col in correct_order if col not in df.columns]
        extra_cols = [col for col in df.columns if col not in correct_order]
        
        if missing_cols:
            print(f"\n❌ Missing columns: {missing_cols}")
            return False
            
        if extra_cols:
            print(f"\n⚠️  Extra columns: {extra_cols}")
        
        # Reorder the DataFrame
        print("\n🔄 Reordering columns...")
        df_reordered = df[correct_order]
        
        # Save the corrected file
        print("💾 Saving corrected MAT_2025.csv...")
        df_reordered.to_csv('MAT_2025.csv', index=False, sep=';', encoding='utf-8-sig')
        
        print("\n✅ Column order fixed successfully!")
        print("\n📋 New column order:")
        for i, col in enumerate(df_reordered.columns, 1):
            print(f"{i:2d}. {col}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error fixing column order: {e}")
        return False

if __name__ == "__main__":
    fix_mat_column_order()
