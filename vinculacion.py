import pandas as pd
import pyodbc
import os
import re
from sqlalchemy import create_engine, text
import urllib.parse
import sys
import codecs
import time
from tqdm import tqdm

# Fix Windows console encoding for Unicode characters
try:
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
except AttributeError:
    # Fallback for older Python versions
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr)

# ENV - Updated for CSV
csv_path = "MANTENEDOR_GRATUIDAD_SEXO.csv"
table_name = 'BI_MANTENEDOR_GRATUIDAD_SEXO'
csv_separator = ';'  # Explicitly set for semicolon-separated files
csv_encoding = 'utf-8'  # Explicitly set encoding

# Database connection parameters (add your credentials here)
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'
# === FUNCIÓN PARA MAPEAR TIPOS DE DATOS ===
def map_dtype_to_sql(dtype, col_name=None):
    if col_name and col_name.lower() == 'monto':
        return "BIGINT"
    if pd.api.types.is_integer_dtype(dtype):
        return "INT"
    elif pd.api.types.is_float_dtype(dtype):
        return "FLOAT"
    elif pd.api.types.is_bool_dtype(dtype):
        return "BIT"
    elif pd.api.types.is_datetime64_any_dtype(dtype):
        return "DATETIME"
    else:
        return "NVARCHAR(MAX)"

# === OPTIMIZED VERSION WITH BULK INSERT AND PROGRESS ===
def fast_bulk_insert():
    start_time = time.time()
    try:
        # Leer CSV con configuración específica
        print("📖 Leyendo archivo CSV...")
        print(f"  📋 Configuración: separador='{csv_separator}', encoding='{csv_encoding}'")
        read_start = time.time()
        
        try:
            df = pd.read_csv(
                csv_path, 
                encoding=csv_encoding,
                sep=csv_separator,
                dtype=str,  # Read everything as string first to avoid parsing issues
                na_values=['', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a', 'None'],
                keep_default_na=True,
                skipinitialspace=True,  # Remove spaces after separator
                quotechar='"',  # Handle quoted fields
                doublequote=True,  # Handle double quotes
                low_memory=False  # Read entire file into memory for better type inference
            )
            print(f"  ✅ CSV leído exitosamente")
            
        except UnicodeDecodeError:
            print(f"  ❌ Error de encoding. Intentando con latin-1...")
            try:
                df = pd.read_csv(
                    csv_path, 
                    encoding='latin-1',
                    sep=csv_separator,
                    dtype=str,
                    na_values=['', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a', 'None'],
                    keep_default_na=True,
                    skipinitialspace=True,
                    quotechar='"',
                    doublequote=True,
                    low_memory=False
                )
                print(f"  ✅ CSV leído con encoding latin-1")
            except Exception as e:
                raise Exception(f"No se pudo leer el CSV: {e}")
        except Exception as e:
            raise Exception(f"Error leyendo CSV: {e}")
        
        read_time = time.time() - read_start
        print(f"✅ CSV leído correctamente en {read_time:.2f}s. Filas: {df.shape[0]:,}, Columnas: {df.shape[1]}")
        
        # Clean column names (remove whitespace, special characters)
        print("\n🧹 Limpiando nombres de columnas...")
        original_columns = df.columns.tolist()
        
        # More aggressive column name cleaning
        cleaned_columns = []
        for col in df.columns:
            # Remove leading/trailing whitespace
            clean_col = col.strip()
            # Replace spaces and special characters with underscores
            clean_col = re.sub(r'[^\w]', '_', clean_col)
            # Remove multiple consecutive underscores
            clean_col = re.sub(r'_+', '_', clean_col)
            # Remove leading/trailing underscores
            clean_col = clean_col.strip('_')
            # Ensure column name is not empty
            if not clean_col:
                clean_col = f'column_{len(cleaned_columns) + 1}'
            cleaned_columns.append(clean_col)
        
        df.columns = cleaned_columns
        
        # Show column names
        print("📋 Columnas detectadas:")
        for i, (orig, clean) in enumerate(zip(original_columns, cleaned_columns)):
            if orig != clean:
                print(f"  {i+1:2d}. '{orig}' -> '{clean}'")
            else:
                print(f"  {i+1:2d}. '{orig}'")
        
        # Check for duplicate column names and fix them
        seen_cols = {}
        final_columns = []
        for col in cleaned_columns:
            if col in seen_cols:
                seen_cols[col] += 1
                final_columns.append(f"{col}_{seen_cols[col]}")
            else:
                seen_cols[col] = 0
                final_columns.append(col)
        
        df.columns = final_columns
        
        # Try to infer better data types after reading as strings
        print("\n🔍 Infiriendo tipos de datos...")
        for col in df.columns:
            # Try to convert to numeric if possible
            try:
                # Check if column looks numeric
                sample_values = df[col].dropna().head(100)
                if len(sample_values) > 0:
                    # Try integer first
                    try:
                        pd.to_numeric(sample_values, downcast='integer')
                        df[col] = pd.to_numeric(df[col], errors='coerce', downcast='integer')
                        print(f"  ✓ '{col}' convertida a entero")
                    except:
                        # Try float
                        try:
                            pd.to_numeric(sample_values, downcast='float')
                            df[col] = pd.to_numeric(df[col], errors='coerce', downcast='float')
                            print(f"  ✓ '{col}' convertida a decimal")
                        except:
                            # Keep as string
                            print(f"  ℹ️ '{col}' mantenida como texto")
            except:
                print(f"  ℹ️ '{col}' mantenida como texto")
        
        # Mostrar tipos de datos finales
        print("\n📊 Tipos de datos finales:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")
        
        # Forzar NVARCHAR en columnas específicas
        print("\n🔄 Procesando columnas especiales...")
        force_nvarchar_columns = {"duracion_meses"}
        
        # Limpieza: asegurarse de que las columnas especiales sean strings
        for col in force_nvarchar_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).fillna("")
                print(f"  ✓ Columna '{col}' convertida a NVARCHAR")
        
        # Crear conexión con SQLAlchemy para bulk operations
        print("\n🔌 Estableciendo conexiones a SQL Server...")
        connection_string_sqlalchemy = (
            f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
            f"?driver=ODBC+Driver+17+for+SQL+Server&charset=utf8"
        )
        
        engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
        
        # También mantener conexión pyodbc para crear tabla
        connection_string_pyodbc = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string_pyodbc)
        cursor = conn.cursor()
        print("✅ Conectado a SQL Server.")
        
        # Definir columnas SQL con NULL explícito y nombres seguros
        print("\n🏗️ Preparando estructura de tabla...")
        column_defs = []
        sql_columns = []
        
        for i, col in enumerate(df.columns):
            # Ensure column name is SQL-safe
            safe_col = col
            if not safe_col or safe_col.isdigit():
                safe_col = f'col_{i+1}'
            
            # Additional SQL safety checks
            if safe_col.upper() in ['ORDER', 'GROUP', 'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX', 'TABLE']:
                safe_col = f'{safe_col}_col'
            
            sql_columns.append(safe_col)
            
            if safe_col in force_nvarchar_columns:
                sql_type = "NVARCHAR(MAX)"
            else:
                sql_type = map_dtype_to_sql(df[col].dtype, col_name=col)
            
            column_defs.append(f"[{safe_col}] {sql_type} NULL")
            print(f"  {col} -> [{safe_col}]: {sql_type}")
        
        # Update DataFrame columns to match SQL-safe names
        df.columns = sql_columns
        
        # Crear tabla si no existe con verificación mejorada
        print(f"\n📋 Creando/verificando tabla '{table_name}'...")
        
        # First, drop table if it exists (optional - remove if you want to append)
        drop_table_query = f"DROP TABLE IF EXISTS [{table_name}]"
        try:
            cursor.execute(drop_table_query)
            conn.commit()
            print(f"  ✓ Tabla existente eliminada")
        except:
            pass
        
        # Create table with proper syntax
        create_table_query = f"""
        CREATE TABLE [{table_name}] (
            {', '.join(column_defs)}
        )
        """
        
        try:
            cursor.execute(create_table_query)
            conn.commit()
            print(f"✅ Tabla '{table_name}' creada exitosamente.")
        except Exception as e:
            print(f"❌ Error creando tabla: {e}")
            print(f"📝 Query problemática:")
            print(create_table_query[:500] + "..." if len(create_table_query) > 500 else create_table_query)
            raise
        
        # Cerrar conexión pyodbc
        conn.close()
        
        # BULK INSERT usando pandas to_sql (mucho más rápido)
        print(f"\n🚀 Iniciando inserción masiva de {len(df):,} filas...")
        print("📊 Configuración: chunks de 500 filas, método multi-row")
        
        # Convertir valores NaN a None para SQL y limpiar datos problemáticos
        print("🔄 Limpiando datos...")
        
        # Clean data more thoroughly
        for col in df.columns:
            # Convert all values to string first, then handle nulls
            df[col] = df[col].astype(str)
            # Replace problematic values
            df[col] = df[col].replace(['nan', 'NaN', 'NULL', 'null', '<NA>', 'None'], None)
            # Convert empty strings to None
            df[col] = df[col].replace(['', ' '], None)
            # Handle very long strings that might cause issues
            df[col] = df[col].apply(lambda x: str(x)[:4000] if x is not None and len(str(x)) > 4000 else x)
        
        # Convert DataFrame using where for final null handling
        df = df.where(pd.notnull(df), None)
        
        # Usar to_sql con método 'multi' para inserción rápida
        insert_start = time.time()
        
        # Usar chunks más pequeños para evitar problemas de memoria/sintaxis
        chunksize = 500  # Reduced from 1000
        total_chunks = (len(df) + chunksize - 1) // chunksize
        print(f"📦 Se procesarán {total_chunks} chunks de {chunksize} filas")
        
        # Custom method para mostrar progreso con mejor manejo de errores
        def insert_with_progress(df, table_name, engine, chunksize=500):
            total_rows = len(df)
            rows_inserted = 0
            failed_chunks = []
            
            # Crear barra de progreso
            with tqdm(total=total_rows, desc="Insertando", unit="filas", 
                     bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]') as pbar:
                
                for i in range(0, total_rows, chunksize):
                    chunk = df.iloc[i:i+chunksize].copy()
                    
                    try:
                        # Clean chunk data one more time
                        for col in chunk.columns:
                            chunk[col] = chunk[col].where(pd.notnull(chunk[col]), None)
                        
                        chunk.to_sql(
                            name=table_name,
                            con=engine,
                            if_exists='append',
                            index=False,
                            method='multi',
                            chunksize=100  # Internal pandas chunking
                        )
                        rows_inserted += len(chunk)
                        pbar.update(len(chunk))
                        
                    except Exception as e:
                        print(f"\n⚠️  Error en chunk {i//chunksize + 1}: {str(e)[:200]}...")
                        failed_chunks.append((i, i+chunksize, str(e)))
                        
                        # Try inserting row by row for failed chunk
                        print(f"  🔄 Intentando inserción fila por fila para chunk {i//chunksize + 1}...")
                        for idx in range(len(chunk)):
                            try:
                                single_row = chunk.iloc[idx:idx+1]
                                single_row.to_sql(
                                    name=table_name,
                                    con=engine,
                                    if_exists='append',
                                    index=False,
                                    method='multi'
                                )
                                rows_inserted += 1
                                pbar.update(1)
                            except Exception as row_error:
                                print(f"    ❌ Fila {i+idx+1} falló: {str(row_error)[:100]}...")
                                pbar.update(1)  # Still update progress
                    
                    # Mostrar estadísticas cada 10 chunks
                    if (i // chunksize + 1) % 10 == 0:
                        elapsed = time.time() - insert_start
                        rate = rows_inserted / elapsed if elapsed > 0 else 0
                        print(f"\n  📈 {rows_inserted:,}/{total_rows:,} filas ({rows_inserted/total_rows*100:.1f}%) - {rate:.0f} filas/seg")
            
            if failed_chunks:
                print(f"\n⚠️  {len(failed_chunks)} chunks tuvieron problemas:")
                for start, end, error in failed_chunks[:5]:  # Show first 5 errors
                    print(f"  - Filas {start+1}-{end}: {error[:100]}...")
            
            return rows_inserted
        
        final_inserted = insert_with_progress(df, table_name, engine, chunksize)
        
        insert_time = time.time() - insert_start
        total_time = time.time() - start_time
        
        print(f"\n🎉 ¡COMPLETADO!")
        print(f"✅ {final_inserted:,} filas insertadas exitosamente en '{table_name}'")
        print(f"⏱️  Tiempo de inserción: {insert_time:.2f}s")
        print(f"⏱️  Tiempo total: {total_time:.2f}s")
        if insert_time > 0:
            print(f"🚀 Velocidad promedio: {final_inserted/insert_time:.0f} filas/segundo")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'engine' in locals():
            engine.dispose()
            print("🔌 Conexión cerrada.")

# === VERSIÓN ALTERNATIVA CON PYODBC OPTIMIZADO Y PROGRESS ===
def fast_pyodbc_insert():
    start_time = time.time()
    try:
        # Leer CSV con configuración específica
        print("📖 Leyendo archivo CSV...")
        print(f"  📋 Configuración: separador='{csv_separator}', encoding='{csv_encoding}'")
        read_start = time.time()
        
        try:
            df = pd.read_csv(
                csv_path, 
                encoding=csv_encoding,
                sep=csv_separator,
                dtype=str,
                na_values=['', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a', 'None'],
                keep_default_na=True,
                skipinitialspace=True,
                quotechar='"',
                doublequote=True,
                low_memory=False
            )
            print(f"  ✅ CSV leído exitosamente")
            
        except UnicodeDecodeError:
            print(f"  ❌ Error de encoding. Intentando con latin-1...")
            try:
                df = pd.read_csv(
                    csv_path, 
                    encoding='latin-1',
                    sep=csv_separator,
                    dtype=str,
                    na_values=['', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a', 'None'],
                    keep_default_na=True,
                    skipinitialspace=True,
                    quotechar='"',
                    doublequote=True,
                    low_memory=False
                )
                print(f"  ✅ CSV leído con encoding latin-1")
            except Exception as e:
                raise Exception(f"No se pudo leer el CSV: {e}")
        except Exception as e:
            raise Exception(f"Error leyendo CSV: {e}")
        
        read_time = time.time() - read_start
        print(f"✅ CSV leído correctamente en {read_time:.2f}s. Filas: {df.shape[0]:,}, Columnas: {df.shape[1]}")
        
        # Clean column names and fix duplicates (same as first function)
        df.columns = df.columns.str.strip()
        
        cleaned_columns = []
        for col in df.columns:
            clean_col = col.strip()
            clean_col = re.sub(r'[^\w]', '_', clean_col)
            clean_col = re.sub(r'_+', '_', clean_col)
            clean_col = clean_col.strip('_')
            if not clean_col:
                clean_col = f'column_{len(cleaned_columns) + 1}'
            cleaned_columns.append(clean_col)
        
        # Fix duplicate column names
        seen_cols = {}
        final_columns = []
        for col in cleaned_columns:
            if col in seen_cols:
                seen_cols[col] += 1
                final_columns.append(f"{col}_{seen_cols[col]}")
            else:
                seen_cols[col] = 0
                final_columns.append(col)
        
        df.columns = final_columns
        
        # Forzar NVARCHAR en columnas específicas
        print("\n🔄 Procesando columnas especiales...")
        force_nvarchar_columns = {"duracion_meses"}
        for col in force_nvarchar_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).fillna("")
                print(f"  ✓ Columna '{col}' convertida a NVARCHAR")
        
        # Conexión con fast_executemany habilitado
        print("\n🔌 Estableciendo conexión optimizada...")
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        conn.autocommit = False  # Usar transacciones para mejor rendimiento
        cursor = conn.cursor()
        cursor.fast_executemany = True  # Habilitar inserción rápida
        
        print("✅ Conectado a SQL Server con fast_executemany habilitado.")
        
        # Crear tabla (same logic as first function but for pyodbc)
        print("\n🏗️ Preparando estructura de tabla...")
        column_defs = []
        sql_columns = []
        
        for i, col in enumerate(df.columns):
            safe_col = col
            if not safe_col or safe_col.isdigit():
                safe_col = f'col_{i+1}'
            
            if safe_col.upper() in ['ORDER', 'GROUP', 'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX', 'TABLE']:
                safe_col = f'{safe_col}_col'
            
            sql_columns.append(safe_col)
            
            if safe_col in force_nvarchar_columns:
                sql_type = "NVARCHAR(MAX)"
            else:
                sql_type = map_dtype_to_sql(df[col].dtype, col_name=col)
            
            column_defs.append(f"[{safe_col}] {sql_type} NULL")
            print(f"  {col}: {sql_type}")
        
        df.columns = sql_columns
        
        # Drop and create table
        drop_table_query = f"DROP TABLE IF EXISTS [{table_name}]"
        try:
            cursor.execute(drop_table_query)
            conn.commit()
        except:
            pass
        
        create_table_query = f"""
        CREATE TABLE [{table_name}] (
            {', '.join(column_defs)}
        )
        """
        cursor.execute(create_table_query)
        conn.commit()
        print(f"✅ Tabla '{table_name}' lista.")
        
        # Preparar datos para inserción masiva con mejor limpieza
        print(f"\n🔄 Preparando {len(df):,} filas para inserción masiva...")
        
        # Clean data more thoroughly before conversion
        for col in df.columns:
            df[col] = df[col].astype(str)
            df[col] = df[col].replace(['nan', 'NaN', 'NULL', 'null', '<NA>', 'None'], None)
            df[col] = df[col].replace(['', ' '], None)
            # Truncate very long strings
            df[col] = df[col].apply(lambda x: str(x)[:4000] if x is not None and len(str(x)) > 4000 else x)
        
        columns = ', '.join(f"[{col}]" for col in df.columns)
        placeholders = ', '.join('?' for _ in df.columns)
        insert_query = f"INSERT INTO [{table_name}] ({columns}) VALUES ({placeholders})"
        
        # Convertir DataFrame a lista de tuplas con barra de progreso y chunks más pequeños
        print("📦 Convirtiendo datos en chunks...")
        
        # Process in smaller chunks to avoid memory issues
        chunk_size = 1000  # Smaller chunks for conversion
        total_chunks = (len(df) + chunk_size - 1) // chunk_size
        
        successful_inserts = 0
        failed_inserts = 0
        
        with tqdm(total=len(df), desc="Procesando e insertando", unit="filas") as pbar:
            for chunk_start in range(0, len(df), chunk_size):
                chunk_end = min(chunk_start + chunk_size, len(df))
                chunk_df = df.iloc[chunk_start:chunk_end].copy()
                
                # Convert chunk to tuples
                data_tuples = []
                for _, row in chunk_df.iterrows():
                    values = tuple(None if pd.isna(val) or str(val).lower() in ['nan', 'none', 'null', ''] else val for val in row)
                    data_tuples.append(values)
                
                # Insert chunk with error handling
                try:
                    cursor.executemany(insert_query, data_tuples)
                    conn.commit()
                    successful_inserts += len(data_tuples)
                    pbar.update(len(data_tuples))
                    
                except Exception as e:
                    print(f"\n⚠️  Error en chunk {chunk_start//chunk_size + 1}: {str(e)[:200]}...")
                    # Try row by row for failed chunk
                    print(f"  🔄 Intentando inserción fila por fila...")
                    
                    for i, row_tuple in enumerate(data_tuples):
                        try:
                            cursor.execute(insert_query, row_tuple)
                            conn.commit()
                            successful_inserts += 1
                        except Exception as row_error:
                            failed_inserts += 1
                            if failed_inserts <= 10:  # Only show first 10 row errors
                                print(f"    ❌ Fila {chunk_start + i + 1}: {str(row_error)[:100]}...")
                        pbar.update(1)
                
                # Show progress every 10 chunks
                if (chunk_start // chunk_size + 1) % 10 == 0:
                    print(f"\n  📈 Procesados {chunk_end:,}/{len(df):,} filas - Exitosas: {successful_inserts:,}, Fallidas: {failed_inserts:,}")
        
        if failed_inserts > 0:
            print(f"\n⚠️  {failed_inserts:,} filas fallaron en la inserción")
        
        insert_time = time.time() - insert_start
        total_time = time.time() - start_time
        
        print(f"\n🎉 ¡COMPLETADO!")
        print(f"✅ {successful_inserts:,} filas insertadas exitosamente en '{table_name}'")
        if failed_inserts > 0:
            print(f"⚠️  {failed_inserts:,} filas fallaron")
        print(f"⏱️  Tiempo de inserción: {insert_time:.2f}s")
        print(f"⏱️  Tiempo total: {total_time:.2f}s")
        if insert_time > 0 and successful_inserts > 0:
            print(f"🚀 Velocidad promedio: {successful_inserts/insert_time:.0f} filas/segundo")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()
            print("🔌 Conexión cerrada.")

# === FUNCIÓN PARA AUTO-DETECTAR SEPARADOR CSV ===
def detect_csv_separator(file_path, sample_size=5):
    """Detecta automáticamente el separador del CSV"""
    import csv
    
    separators = [',', ';', '\t', '|']
    
    with open(file_path, 'r', encoding='utf-8') as file:
        sample = file.read(1024 * sample_size)  # Lee los primeros KB
        
    sniffer = csv.Sniffer()
    try:
        dialect = sniffer.sniff(sample, delimiters=',;\t|')
        return dialect.delimiter
    except:
        # Fallback: contar ocurrencias de cada separador
        separator_counts = {sep: sample.count(sep) for sep in separators}
        return max(separator_counts, key=separator_counts.get)

# === EJECUCIÓN ===
if __name__ == "__main__":
    print("🚀 INICIANDO IMPORTACIÓN CSV → SQL SERVER")
    print("=" * 50)
    
    # Detectar separador automáticamente
    try:
        detected_sep = detect_csv_separator(csv_path)
        print(f"🔍 Separador detectado: '{detected_sep}'")
    except Exception as e:
        print(f"⚠️  No se pudo detectar separador automáticamente: {e}")
        print("📝 Usando separador por defecto: ','")
    
    # Usar la versión con SQLAlchemy (más rápida para grandes volúmenes)
    print("\n📊 MÉTODO 1: SQLAlchemy Bulk Insert (RECOMENDADO)")
    print("-" * 50)
    fast_bulk_insert()
    
    # Alternativa con pyodbc optimizado
    # print("\n📊 MÉTODO 2: PyODBC Fast ExecuteMany")
    # print("-" * 50)
    # fast_pyodbc_insert()
    
    print("\n" + "=" * 50)
    print("🎯 PROCESO COMPLETADO")
    
    # Mostrar información adicional
    print("\n💡 CONSEJOS PARA OPTIMIZACIÓN CSV:")
    print("• Verifica que el encoding del CSV sea correcto (UTF-8 recomendado)")
    print("• Para archivos >50k filas, considera usar BULK INSERT de SQL Server")
    print("• Si hay caracteres especiales, prueba diferentes encodings")
    print("• Verifica que el separador sea detectado correctamente")
    print("• Para CSVs muy grandes, considera procesarlos en chunks")