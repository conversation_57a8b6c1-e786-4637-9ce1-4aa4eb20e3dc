#!/usr/bin/env python3
"""
Test script to verify API connection and explore available data.
"""

import requests
import json

# API credentials
BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
USERNAME = '48bc01ad5501e88f249831516829efeb'
PASSWORD = '030643a80361e103'

def test_endpoint(endpoint, params=None):
    """Test a single endpoint and show sample data."""
    url = f"{BASE_URL}/{endpoint}"
    
    try:
        print(f"\n--- Testing {endpoint} ---")
        response = requests.get(url, auth=(USERNAME, PASSWORD), params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        if isinstance(data, list):
            print(f"✓ Success! Got {len(data)} records")
            if len(data) > 0:
                print("Sample record:")
                print(json.dumps(data[0], indent=2, ensure_ascii=False))
        else:
            print(f"✓ Success! Got single record")
            print("Data:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Request error: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"✗ JSON parse error: {e}")
        print(f"Raw response: {response.text[:200]}...")
        return False

def main():
    """Test API connection with a few endpoints."""
    print("Testing API connection...")
    
    # Test endpoints
    test_endpoints = [
        ("carreras_alumnos", None),
        ("carreras", None),
        ("instituciones", None),
        ("ramos", None),
        ("cursos", None)
    ]
    
    successful = 0
    for endpoint, params in test_endpoints:
        if test_endpoint(endpoint, params):
            successful += 1
    
    print(f"\n--- Summary ---")
    print(f"Successful tests: {successful}/{len(test_endpoints)}")
    
    if successful > 0:
        print("✓ API connection is working!")
    else:
        print("✗ API connection failed. Check credentials or network.")

if __name__ == "__main__":
    main()
