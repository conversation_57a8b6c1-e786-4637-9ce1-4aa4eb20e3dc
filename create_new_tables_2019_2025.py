#!/usr/bin/env python3
"""
Create new tables for 2019-2025 pregrado data and rollback changes to existing tables.

This script will:
1. Create new table: MAT_2019_2025_25092025
2. Create new table: BI_NOTAS_2019_2025_STG_Ucampus_25092025  
3. Rollback changes to MAT_07_24 (remove records with ID >= 100000)
4. Rollback changes to BI_NOTAS_07012025_STG_Ucampus (remove 2019-2025 records)
"""

import pandas as pd
import pyodbc
import time
import os
from sqlalchemy import create_engine, text
import urllib.parse
from tqdm import tqdm

# Database connection parameters (updated with current credentials)
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

# Connection strings (using exact same format as working script)
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
connection_string_sqlalchemy = (
    f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
    f"?driver=ODBC+Driver+17+for+SQL+Server"
)

def rollback_existing_tables():
    """Rollback changes made to existing tables."""
    print("\n" + "="*60)
    print("ROLLING BACK CHANGES TO EXISTING TABLES")
    print("="*60)
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Rollback MAT_07_24 - remove records with ID >= 100000
        print("🔄 Rolling back MAT_07_24 table...")
        cursor.execute("SELECT COUNT(*) FROM [MAT_07_24] WHERE [ID] >= 100000")
        count_to_delete = cursor.fetchone()[0]
        print(f"📊 Found {count_to_delete} records to remove from MAT_07_24")
        
        if count_to_delete > 0:
            cursor.execute("DELETE FROM [MAT_07_24] WHERE [ID] >= 100000")
            conn.commit()
            print(f"✅ Removed {count_to_delete} records from MAT_07_24")
        else:
            print("ℹ️  No records to remove from MAT_07_24")
        
        # Rollback BI_NOTAS - remove 2019-2025 records that were just added
        print("\n🔄 Rolling back BI_NOTAS_07012025_STG_Ucampus table...")
        cursor.execute("SELECT COUNT(*) FROM [BI_NOTAS_07012025_STG_Ucampus] WHERE [annio] BETWEEN 2019 AND 2025")
        total_2019_2025 = cursor.fetchone()[0]
        print(f"📊 Total 2019-2025 records in BI_NOTAS: {total_2019_2025}")
        
        # We need to be more careful here - only remove the records we just added
        # Let's check if there were existing 2019-2025 records before our import
        print("⚠️  WARNING: This will remove ALL 2019-2025 records from BI_NOTAS table")
        print("   If you had existing 2019-2025 data, it will also be removed.")
        
        response = input("Do you want to proceed with removing ALL 2019-2025 records? (y/N): ")
        if response.lower() == 'y':
            cursor.execute("DELETE FROM [BI_NOTAS_07012025_STG_Ucampus] WHERE [annio] BETWEEN 2019 AND 2025")
            conn.commit()
            print(f"✅ Removed {total_2019_2025} records from BI_NOTAS_07012025_STG_Ucampus")
        else:
            print("ℹ️  Skipped BI_NOTAS rollback")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during rollback: {e}")
        return False

def create_mat_table():
    """Create new MAT_2019_2025_25092025 table and insert data."""
    print("\n" + "="*60)
    print("CREATING MAT_2019_2025_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    mat_file = "MAT_2019_2025_25092025.csv"
    if not os.path.exists(mat_file):
        print(f"❌ File not found: {mat_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {mat_file}...")
    df = pd.read_csv(mat_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    print(f"✓ ID range: {df['ID'].min()} to {df['ID'].max()}")
    
    # Create SQLAlchemy engine
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    try:
        # Drop table if exists and create new one
        table_name = "MAT_2019_2025_25092025"
        print(f"🗑️  Dropping table {table_name} if it exists...")
        
        with engine.connect() as conn:
            conn.execute(text(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE [{table_name}]"))
            conn.commit()
        
        # Insert data - this will create the table automatically
        print(f"🚀 Creating table and inserting {len(df)} records...")
        
        # Use tqdm for progress bar
        chunk_size = 1000
        total_chunks = len(df) // chunk_size + (1 if len(df) % chunk_size else 0)
        
        with tqdm(total=len(df), desc="Inserting MAT data", unit="rows") as pbar:
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                chunk.to_sql(table_name, engine, if_exists='append', index=False, method='multi')
                pbar.update(len(chunk))
        
        print(f"✅ Successfully created table {table_name} with {len(df)} records")
        return True
        
    except Exception as e:
        print(f"❌ Error creating MAT table: {e}")
        return False
    finally:
        engine.dispose()

def create_bi_notas_table():
    """Create new BI_NOTAS_2019_2025_STG_Ucampus_25092025 table and insert data."""
    print("\n" + "="*60)
    print("CREATING BI_NOTAS_2019_2025_STG_Ucampus_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    bi_file = "BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv"
    if not os.path.exists(bi_file):
        print(f"❌ File not found: {bi_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {bi_file}...")
    df = pd.read_csv(bi_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    
    # Create SQLAlchemy engine
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    try:
        # Drop table if exists and create new one
        table_name = "BI_NOTAS_2019_2025_STG_Ucampus_25092025"
        print(f"🗑️  Dropping table {table_name} if it exists...")
        
        with engine.connect() as conn:
            conn.execute(text(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE [{table_name}]"))
            conn.commit()
        
        # Insert data - this will create the table automatically
        print(f"🚀 Creating table and inserting {len(df)} records...")
        
        # Use tqdm for progress bar
        chunk_size = 1000
        
        with tqdm(total=len(df), desc="Inserting BI_NOTAS data", unit="rows") as pbar:
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                chunk.to_sql(table_name, engine, if_exists='append', index=False, method='multi')
                pbar.update(len(chunk))
        
        print(f"✅ Successfully created table {table_name} with {len(df)} records")
        return True
        
    except Exception as e:
        print(f"❌ Error creating BI_NOTAS table: {e}")
        return False
    finally:
        engine.dispose()

def verify_new_tables():
    """Verify the new tables were created successfully."""
    print("\n" + "="*60)
    print("VERIFICATION")
    print("="*60)
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Check MAT table
        cursor.execute("SELECT COUNT(*) FROM [MAT_2019_2025_25092025]")
        mat_count = cursor.fetchone()[0]
        print(f"📊 MAT_2019_2025_25092025 records: {mat_count:,}")
        
        # Check BI_NOTAS table
        cursor.execute("SELECT COUNT(*) FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025]")
        bi_count = cursor.fetchone()[0]
        print(f"📊 BI_NOTAS_2019_2025_STG_Ucampus_25092025 records: {bi_count:,}")
        
        # Show year breakdown for BI_NOTAS
        print(f"\n📋 BI_NOTAS breakdown by year:")
        cursor.execute("""
            SELECT [annio], COUNT(*) as count 
            FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025] 
            GROUP BY [annio] 
            ORDER BY [annio]
        """)
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1]:,} records")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function."""
    print("🚀 CREATING NEW TABLES FOR 2019-2025 PREGRADO DATA")
    print("="*60)
    print(f"📊 Target Server: {server}")
    print(f"📊 Target Database: {database}")
    print(f"📊 New tables to create:")
    print(f"   • MAT_2019_2025_25092025")
    print(f"   • BI_NOTAS_2019_2025_STG_Ucampus_25092025")
    print("ℹ️  Skipping rollback - only creating new tables")
    
    start_time = time.time()
    
    # Skip rollback - just create new tables
    rollback_success = True  # Skip rollback

    # Step 1: Create new MAT table
    mat_success = create_mat_table()

    # Step 2: Create new BI_NOTAS table
    bi_success = create_bi_notas_table()

    # Step 3: Verify new tables
    verify_success = verify_new_tables()
    
    # Final summary
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    print(f"MAT table creation: {'✅ SUCCESS' if mat_success else '❌ FAILED'}")
    print(f"BI_NOTAS table creation: {'✅ SUCCESS' if bi_success else '❌ FAILED'}")
    print(f"Verification: {'✅ SUCCESS' if verify_success else '❌ FAILED'}")
    print(f"⏱️  Total time: {total_time:.2f}s")

    if all([mat_success, bi_success, verify_success]):
        print("\n🎉 All operations completed successfully!")
        print("\n📋 Next steps:")
        print("   • Use MAT_2019_2025_25092025 for matriculation data")
        print("   • Use BI_NOTAS_2019_2025_STG_Ucampus_25092025 for grades data")
        print("   • Check FOBI13 course data in the new BI_NOTAS table")
    else:
        print("\n⚠️  Some operations failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
