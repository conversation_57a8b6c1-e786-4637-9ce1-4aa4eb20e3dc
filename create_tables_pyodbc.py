#!/usr/bin/env python3
"""
Create new tables using pyodbc directly (same approach as working script).
"""

import pandas as pd
import pyodbc
import time
import os
from tqdm import tqdm

# Database connection parameters
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

# Connection string
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

def create_mat_table():
    """Create MAT_2019_2025_25092025 table using pyodbc directly."""
    print("\n" + "="*60)
    print("CREATING MAT_2019_2025_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    mat_file = "MAT_2019_2025_25092025.csv"
    if not os.path.exists(mat_file):
        print(f"❌ File not found: {mat_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {mat_file}...")
    df = pd.read_csv(mat_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    print(f"✓ ID range: {df['ID'].min()} to {df['ID'].max()}")
    
    # Handle data type issues and replace NaN with None for SQL compatibility
    print("🔧 Cleaning data for SQL compatibility...")

    # Convert problematic float columns that are all NaN to object type first
    float_cols_all_nan = []
    for col in df.columns:
        if df[col].dtype == 'float64' and df[col].isnull().all():
            float_cols_all_nan.append(col)
            df[col] = df[col].astype('object')

    if float_cols_all_nan:
        print(f"   Converted all-NaN float columns to object: {float_cols_all_nan}")

    # Replace NaN with None for SQL compatibility
    df = df.where(pd.notnull(df), None)

    # Handle very large N_DOC values that might cause issues
    if 'N_DOC' in df.columns:
        max_n_doc = df['N_DOC'].max()
        if max_n_doc > 2147483647:  # SQL Server int max value
            print(f"   ⚠️  N_DOC has very large values (max: {max_n_doc}), this might cause issues")

    print("✓ Data cleaning completed")
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Drop table if exists
        table_name = "MAT_2019_2025_25092025"
        print(f"🗑️  Dropping table {table_name} if it exists...")
        cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE [{table_name}]")
        conn.commit()
        
        # Create table with same structure as MAT_07_24
        print(f"🏗️  Creating table {table_name}...")
        create_table_sql = f"""
        CREATE TABLE [{table_name}] (
            [ID] [int] NOT NULL,
            [TIPO_DOC] [int] NULL,
            [N_DOC] [int] NULL,
            [DV] [varchar](1) NULL,
            [PRIMER_APELLIDO] [varchar](50) NULL,
            [SEGUNDO_APELLIDO] [varchar](50) NULL,
            [NOMBRE] [varchar](50) NULL,
            [SEXO] [varchar](1) NULL,
            [FECH_NAC] [date] NULL,
            [NAC] [varchar](2) NULL,
            [FOR_ING_ACT] [int] NULL,
            [ANIO_ING_ACT] [int] NULL,
            [SEM_ING_ACT] [int] NULL,
            [ANIO_ING_ORI] [int] NULL,
            [SEM_ING_ORI] [int] NULL,
            [VIG] [int] NULL,
            [AÑO_PROCESO] [int] NULL,
            [COD_SIES] [int] NULL,
            [NOMBRE_CARRERA] [varchar](150) NULL,
            [FACULTAD] [varchar](100) NULL,
            [LLAVE_SIES] [varchar](20) NULL,
            [COHORTE_ESTUDIANTE] [int] NULL,
            [LLAVE_RUT_AÑO] [varchar](20) NULL,
            [LLAVE_RUT_COHORTE] [varchar](20) NULL,
            [TIPO_PLAN] [varchar](50) NULL,
            [NIVEL] [varchar](50) NULL,
            [ÁREA DEL CONOCIMIENTO] [varchar](100) NULL,
            [CLASIFICACION_NIVEL_1] [varchar](50) NULL,
            [CLASIFICACION_NIVEL_2] [varchar](50) NULL
        )
        """
        cursor.execute(create_table_sql)
        conn.commit()
        print(f"✅ Table {table_name} created successfully")
        
        # Insert data in batches
        print(f"🚀 Inserting {len(df)} records...")
        insert_sql = f"""
        INSERT INTO [{table_name}] 
        ([ID], [TIPO_DOC], [N_DOC], [DV], [PRIMER_APELLIDO], [SEGUNDO_APELLIDO], [NOMBRE], [SEXO], [FECH_NAC], [NAC], 
         [FOR_ING_ACT], [ANIO_ING_ACT], [SEM_ING_ACT], [ANIO_ING_ORI], [SEM_ING_ORI], [VIG], [AÑO_PROCESO], [COD_SIES], 
         [NOMBRE_CARRERA], [FACULTAD], [LLAVE_SIES], [COHORTE_ESTUDIANTE], [LLAVE_RUT_AÑO], [LLAVE_RUT_COHORTE], 
         [TIPO_PLAN], [NIVEL], [ÁREA DEL CONOCIMIENTO], [CLASIFICACION_NIVEL_1], [CLASIFICACION_NIVEL_2])
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        # Convert DataFrame to list of tuples
        data_tuples = [tuple(row) for row in df.values]
        
        # Insert in batches with progress bar (larger batch size for speed)
        batch_size = 5000
        total_batches = len(data_tuples) // batch_size + (1 if len(data_tuples) % batch_size else 0)
        
        with tqdm(total=len(data_tuples), desc="Inserting MAT data", unit="rows") as pbar:
            for i in range(0, len(data_tuples), batch_size):
                batch = data_tuples[i:i+batch_size]
                cursor.executemany(insert_sql, batch)
                conn.commit()
                pbar.update(len(batch))
        
        conn.close()
        print(f"✅ Successfully inserted {len(df)} records into {table_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating MAT table: {e}")
        return False

def create_bi_notas_table():
    """Create BI_NOTAS_2019_2025_STG_Ucampus_25092025 table using pyodbc directly."""
    print("\n" + "="*60)
    print("CREATING BI_NOTAS_2019_2025_STG_Ucampus_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    bi_file = "BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv"
    if not os.path.exists(bi_file):
        print(f"❌ File not found: {bi_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {bi_file}...")
    df = pd.read_csv(bi_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    
    # Handle data type issues and replace NaN with None for SQL compatibility
    print("🔧 Cleaning data for SQL compatibility...")

    # Convert problematic float columns that are all NaN to object type first
    float_cols_all_nan = []
    for col in df.columns:
        if df[col].dtype == 'float64' and df[col].isnull().all():
            float_cols_all_nan.append(col)
            df[col] = df[col].astype('object')

    if float_cols_all_nan:
        print(f"   Converted all-NaN float columns to object: {float_cols_all_nan}")

    # Replace NaN with None for SQL compatibility
    df = df.where(pd.notnull(df), None)

    print("✓ Data cleaning completed")
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Drop table if exists
        table_name = "BI_NOTAS_2019_2025_STG_Ucampus_25092025"
        print(f"🗑️  Dropping table {table_name} if it exists...")
        cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE [{table_name}]")
        conn.commit()
        
        # Create table with same structure as BI_NOTAS_07012025_STG_Ucampus
        print(f"🏗️  Creating table {table_name}...")
        create_table_sql = f"""
        CREATE TABLE [{table_name}] (
            [llave] [varchar](50) NULL,
            [annio] [int] NULL,
            [semestre] [int] NULL,
            [estado] [varchar](20) NULL,
            [annio_ingreso] [int] NULL,
            [tipo_ingreso] [varchar](50) NULL,
            [rut] [int] NULL,
            [dv] [varchar](1) NULL,
            [nombres] [varchar](100) NULL,
            [apellidos] [varchar](100) NULL,
            [id_curso] [int] NULL,
            [codigo] [varchar](20) NULL,
            [nombre] [varchar](200) NULL,
            [estado_curso] [varchar](50) NULL,
            [nota] [float] NULL,
            [estado_final] [varchar](20) NULL,
            [sct] [int] NULL,
            [Inscrito] [int] NULL,
            [Eliminado] [int] NULL,
            [Aprobado] [int] NULL,
            [Reprobado] [int] NULL,
            [N] [int] NULL,
            [departamento] [varchar](100) NULL,
            [codigo_asignatura_2] [varchar](20) NULL,
            [comentario] [varchar](500) NULL
        )
        """
        cursor.execute(create_table_sql)
        conn.commit()
        print(f"✅ Table {table_name} created successfully")
        
        # Insert data in batches
        print(f"🚀 Inserting {len(df)} records...")
        insert_sql = f"""
        INSERT INTO [{table_name}] 
        ([llave], [annio], [semestre], [estado], [annio_ingreso], [tipo_ingreso], [rut], [dv], [nombres], [apellidos], 
         [id_curso], [codigo], [nombre], [estado_curso], [nota], [estado_final], [sct], [Inscrito], [Eliminado], 
         [Aprobado], [Reprobado], [N], [departamento], [codigo_asignatura_2], [comentario])
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        # Convert DataFrame to list of tuples
        data_tuples = [tuple(row) for row in df.values]
        
        # Insert in batches with progress bar (larger batch size for speed)
        batch_size = 10000
        
        with tqdm(total=len(data_tuples), desc="Inserting BI_NOTAS data", unit="rows") as pbar:
            for i in range(0, len(data_tuples), batch_size):
                batch = data_tuples[i:i+batch_size]
                cursor.executemany(insert_sql, batch)
                conn.commit()
                pbar.update(len(batch))
        
        conn.close()
        print(f"✅ Successfully inserted {len(df)} records into {table_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating BI_NOTAS table: {e}")
        return False

def verify_tables():
    """Verify the new tables were created successfully."""
    print("\n" + "="*60)
    print("VERIFICATION")
    print("="*60)
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Check MAT table
        cursor.execute("SELECT COUNT(*) FROM [MAT_2019_2025_25092025]")
        mat_count = cursor.fetchone()[0]
        print(f"📊 MAT_2019_2025_25092025 records: {mat_count:,}")
        
        # Check BI_NOTAS table
        cursor.execute("SELECT COUNT(*) FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025]")
        bi_count = cursor.fetchone()[0]
        print(f"📊 BI_NOTAS_2019_2025_STG_Ucampus_25092025 records: {bi_count:,}")
        
        # Show year breakdown for BI_NOTAS
        if bi_count > 0:
            print(f"\n📋 BI_NOTAS breakdown by year:")
            cursor.execute("""
                SELECT [annio], COUNT(*) as count 
                FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025] 
                GROUP BY [annio] 
                ORDER BY [annio]
            """)
            for row in cursor.fetchall():
                print(f"  {row[0]}: {row[1]:,} records")
        
        # Quick FOBI13 check
        if bi_count > 0:
            print(f"\n🔍 FOBI13 Quick Check:")
            cursor.execute("""
                SELECT [annio], COUNT(*) as total, 
                       SUM(CASE WHEN [estado_final] = 'Aprobado' THEN 1 ELSE 0 END) as aprobados
                FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025] 
                WHERE [codigo] = 'FOBI13'
                GROUP BY [annio] 
                ORDER BY [annio]
            """)
            fobi13_data = cursor.fetchall()
            if fobi13_data:
                for row in fobi13_data:
                    year, total, aprobados = row
                    tasa = (aprobados/total*100) if total > 0 else 0
                    print(f"  {year}: {total} students, {aprobados} approved, {tasa:.1f}% approval rate")
            else:
                print("  No FOBI13 data found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function."""
    print("🚀 CREATING NEW TABLES FOR 2019-2025 PREGRADO DATA")
    print("="*60)
    print(f"📊 Target Server: {server}")
    print(f"📊 Target Database: {database}")
    print(f"📊 New tables to create:")
    print(f"   • MAT_2019_2025_25092025")
    print(f"   • BI_NOTAS_2019_2025_STG_Ucampus_25092025")
    
    start_time = time.time()
    
    # Create new MAT table
    mat_success = create_mat_table()
    
    # Create new BI_NOTAS table
    bi_success = create_bi_notas_table()
    
    # Verify new tables
    verify_success = verify_tables()
    
    # Final summary
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    print(f"MAT table creation: {'✅ SUCCESS' if mat_success else '❌ FAILED'}")
    print(f"BI_NOTAS table creation: {'✅ SUCCESS' if bi_success else '❌ FAILED'}")
    print(f"Verification: {'✅ SUCCESS' if verify_success else '❌ FAILED'}")
    print(f"⏱️  Total time: {total_time:.2f}s")
    
    if all([mat_success, bi_success, verify_success]):
        print("\n🎉 All operations completed successfully!")
        print("\n📋 Next steps:")
        print("   • Use MAT_2019_2025_25092025 for matriculation data")
        print("   • Use BI_NOTAS_2019_2025_STG_Ucampus_25092025 for grades data")
        print("   • Check FOBI13 course data in the new BI_NOTAS table")
        print("   • Update your Power BI reports to use the new tables")
        print("\n🎯 Expected FOBI13 Results:")
        print("   • Should show 35+ students per year (not 3-8)")
        print("   • Should show 92-97% approval rates (not 0%)")
    else:
        print("\n⚠️  Some operations failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
