#!/usr/bin/env python3
"""
Transform API data to match BI_NOTAS and MAT structure for 2025 data.
Creates files compatible with existing Power BI structure.
"""

import pandas as pd
import os
from datetime import datetime
import numpy as np

def load_api_data():
    """Load all API data files."""
    data = {}
    api_dir = "api_data_2025"
    
    if not os.path.exists(api_dir):
        print(f"Error: {api_dir} directory not found. Run the API fetch scripts first.")
        return None
    
    files_to_load = [
        'cursos_inscritos_2025.csv',
        'carreras_alumnos_2025.csv', 
        'personas_2025.csv',
        'cursos_2025.csv',
        'ramos_2025.csv',
        'carreras_2025.csv',
        'instituciones_2025.csv',
        'planes_2025.csv'
    ]
    
    for filename in files_to_load:
        filepath = os.path.join(api_dir, filename)
        if os.path.exists(filepath):
            try:
                df = pd.read_csv(filepath)
                key = filename.replace('_2025.csv', '')
                data[key] = df
                print(f"✓ Loaded {len(df)} records from {filename}")
            except Exception as e:
                print(f"✗ Error loading {filename}: {e}")
        else:
            print(f"⚠ File not found: {filename}")
    
    return data

def create_bi_notas_structure(data, year=2025):
    """
    Create BI_NOTAS structure from API data.
    Based on: cursos_inscritos + personas + cursos + ramos + instituciones
    """
    print(f"\n--- Creating BI_NOTAS structure for {year} ---")
    
    # Start with cursos_inscritos (course enrollments)
    if 'cursos_inscritos' not in data:
        print("Error: cursos_inscritos data not found")
        return None
    
    bi_notas = data['cursos_inscritos'].copy()
    
    # Add person information
    if 'personas' in data:
        personas_cols = ['rut', 'dv', 'nombre1', 'nombre2', 'apellido1', 'apellido2']
        personas_subset = data['personas'][personas_cols].drop_duplicates('rut')
        bi_notas = bi_notas.merge(personas_subset, on='rut', how='left')
    
    # Add course information (cursos_inscritos already has most course info)
    # The cursos_inscritos endpoint already contains: codigo, nombre, sct, ud
    # So we don't need to merge additional course data
    
    # Add institution information
    if 'instituciones' in data:
        inst_cols = ['id_institucion', 'nombre']
        inst_subset = data['instituciones'][inst_cols].drop_duplicates('id_institucion')
        inst_subset = inst_subset.rename(columns={'nombre': 'departamento'})
        bi_notas = bi_notas.merge(inst_subset, on='id_institucion', how='left')
    
    # Transform to match BI_NOTAS structure
    bi_notas_transformed = pd.DataFrame()
    
    # Create the main columns
    bi_notas_transformed['llave'] = (bi_notas['rut'].astype(str) + '-' + 
                                   bi_notas['id_curso'].astype(str) + '-' + 
                                   bi_notas['codigo'].astype(str) + '-' + str(year))
    
    bi_notas_transformed['annio'] = year
    bi_notas_transformed['semestre'] = bi_notas['periodo']
    bi_notas_transformed['estado'] = 'Activo'  # Default, could be enhanced with more logic
    bi_notas_transformed['annio_ingreso'] = year  # Could be enhanced with actual data
    bi_notas_transformed['tipo_ingreso'] = 'Ingreso Regular'  # Default
    
    # Person information
    bi_notas_transformed['rut'] = bi_notas['rut']
    bi_notas_transformed['dv'] = bi_notas['dv'].fillna(0)
    bi_notas_transformed['nombres'] = (bi_notas['nombre1'].fillna('') + ' ' +
                                     bi_notas['nombre2'].fillna('')).str.strip()
    bi_notas_transformed['apellidos'] = (bi_notas['apellido1'].fillna('') + ' ' +
                                       bi_notas['apellido2'].fillna('')).str.strip()
    
    # Course information
    bi_notas_transformed['id_curso'] = bi_notas['id_curso']
    bi_notas_transformed['codigo'] = bi_notas['codigo']
    bi_notas_transformed['nombre'] = bi_notas['nombre']
    bi_notas_transformed['estado_curso'] = bi_notas['estado_curso'].fillna('Aceptado')
    bi_notas_transformed['nota'] = bi_notas['nota_final'].fillna(0.0)
    bi_notas_transformed['estado_final'] = bi_notas['estado_final'].fillna('Inscrito')
    bi_notas_transformed['sct'] = bi_notas['sct'].fillna(0)
    
    # Status flags (based on estado_final)
    bi_notas_transformed['Inscrito'] = (bi_notas_transformed['estado_final'] == 'Inscrito').astype(int)
    bi_notas_transformed['Eliminado'] = (bi_notas_transformed['estado_final'] == 'Eliminado').astype(int)
    bi_notas_transformed['Aprobado'] = (bi_notas_transformed['estado_final'] == 'Aprobado').astype(int)
    bi_notas_transformed['Reprobado'] = (bi_notas_transformed['estado_final'] == 'Reprobado').astype(int)
    bi_notas_transformed['N'] = 1  # Count flag
    
    # Department
    bi_notas_transformed['departamento'] = bi_notas['departamento'].fillna('Otros')
    bi_notas_transformed['codigo_ asignatura_2'] = ''  # Empty as in original
    bi_notas_transformed['comentario'] = ''  # Empty as in original
    
    # Key field
    bi_notas_transformed['LLAVE_RUT_AÑO'] = (bi_notas_transformed['rut'].astype(str) + '-' + str(year))
    bi_notas_transformed['Columna'] = ''  # Empty as in original
    
    print(f"✓ Created BI_NOTAS structure with {len(bi_notas_transformed)} records")
    return bi_notas_transformed

def create_mat_structure(data, year=2025):
    """
    Create MAT structure from API data.
    Based on: carreras_alumnos + personas + carreras + planes
    """
    print(f"\n--- Creating MAT structure for {year} ---")
    
    # Start with carreras_alumnos (student-career relationships)
    if 'carreras_alumnos' not in data:
        print("Error: carreras_alumnos data not found")
        return None
    
    mat = data['carreras_alumnos'].copy()
    
    # Add person information
    if 'personas' in data:
        personas_cols = ['rut', 'dv', 'apellido1', 'apellido2', 'nombre1', 'nombre2',
                        'genero', 'fecha_nacimiento', 'anno_ingreso', 'tipo_ingreso']
        personas_subset = data['personas'][personas_cols].drop_duplicates('rut')
        mat = mat.merge(personas_subset, on='rut', how='left')
    
    # Add career information
    if 'carreras' in data:
        carreras_cols = ['id_carrera', 'codigo_carrera', 'nombre', 'tipo_titulo_texto', 'institucion']
        carreras_subset = data['carreras'][carreras_cols].drop_duplicates('id_carrera')
        carreras_subset = carreras_subset.rename(columns={'nombre': 'nombre_carrera', 'institucion': 'facultad'})
        mat = mat.merge(carreras_subset, on='id_carrera', how='left', suffixes=('', '_carrera'))
    
    # Transform to match MAT structure
    mat_transformed = pd.DataFrame()
    
    # Create sequential ID
    mat_transformed['ID'] = range(1, len(mat) + 1)
    mat_transformed['TIPO_DOC'] = 'R'  # Assuming RUT
    mat_transformed['N_DOC'] = mat['rut']
    mat_transformed['DV'] = mat['dv'].fillna(0)
    
    # Names
    mat_transformed['PRIMER_APELLIDO'] = mat['apellido1'].fillna('')
    mat_transformed['SEGUNDO_APELLIDO'] = mat['apellido2'].fillna('')
    mat_transformed['NOMBRE'] = (mat['nombre1'].fillna('') + ' ' + mat['nombre2'].fillna('')).str.strip()

    # Demographics
    mat_transformed['SEXO'] = mat['genero'].fillna('No Definido')
    mat_transformed['FECH_NAC'] = mat['fecha_nacimiento'].fillna('')
    mat_transformed['NAC'] = 152  # Default Chile code

    # Academic information - CORRECT ORDER: FOR_ING_ACT, ANIO_ING_ACT, SEM_ING_ACT, ANIO_ING_ORI, SEM_ING_ORI
    mat_transformed['FOR_ING_ACT'] = 10  # Default form of entry

    # Academic years (use personas data if available, otherwise extract from periodo_inicio)
    if 'anno_ingreso' in mat.columns and mat['anno_ingreso'].notna().any():
        mat_transformed['ANIO_ING_ACT'] = mat['anno_ingreso'].fillna(year)
        mat_transformed['ANIO_ING_ORI'] = mat['anno_ingreso'].fillna(year)
    else:
        # Extract year from periodo_inicio (format: YYYY.S)
        periodo_inicio_year = mat['periodo_inicio'].astype(str).str.split('.').str[0]
        mat_transformed['ANIO_ING_ACT'] = pd.to_numeric(periodo_inicio_year, errors='coerce').fillna(year)
        mat_transformed['ANIO_ING_ORI'] = pd.to_numeric(periodo_inicio_year, errors='coerce').fillna(year)

    mat_transformed['SEM_ING_ACT'] = 1  # Default first semester
    mat_transformed['SEM_ING_ORI'] = 1  # Default first semester
    mat_transformed['VIG'] = 1  # Active
    mat_transformed['AÑO_PROCESO'] = year
    
    # Career information
    mat_transformed['COD_SIES'] = mat['codigo_carrera'].fillna('')
    mat_transformed['NOMBRE_CARRERA'] = mat['nombre_carrera'].fillna('')
    mat_transformed['FACULTAD'] = mat['facultad'].fillna('')
    
    # Keys
    mat_transformed['LLAVE_SIES'] = str(year) + '-' + mat_transformed['COD_SIES'].astype(str)
    mat_transformed['COHORTE_ESTUDIANTE'] = 'Cohorte principal'
    mat_transformed['LLAVE_RUT_AÑO'] = (mat_transformed['N_DOC'].astype(str) + '-' + str(year))
    mat_transformed['LLAVE_RUT_COHORTE'] = (mat_transformed['N_DOC'].astype(str) + '-' + 
                                          mat_transformed['ANIO_ING_ORI'].astype(str))
    
    # Plan and classification
    mat_transformed['TIPO_PLAN'] = 'Plan Regular'
    mat_transformed['NIVEL'] = 'Pregrado'
    mat_transformed['ÁREA DEL CONOCIMIENTO'] = 'Tecnología'  # Default, could be enhanced
    mat_transformed['CLASIFICACION_NIVEL_1'] = mat['tipo_titulo_texto'].fillna('Profesional Con Licenciatura')
    mat_transformed['CLASIFICACION_NIVEL_2'] = 'Carreras Profesionales'
    
    print(f"✓ Created MAT structure with {len(mat_transformed)} records")
    return mat_transformed

def main():
    """Main function to transform API data to BI format."""
    print("=" * 60)
    print("API TO BI FORMAT TRANSFORMER - 2025")
    print("=" * 60)
    
    # Load API data
    data = load_api_data()
    if not data:
        return
    
    # Create BI_NOTAS structure
    bi_notas_2025 = create_bi_notas_structure(data, 2025)
    if bi_notas_2025 is not None:
        filename = "BI_NOTAS_2025_STG_Ucampus.csv"
        bi_notas_2025.to_csv(filename, index=False, sep=';', encoding='utf-8-sig')
        print(f"✓ Saved BI_NOTAS format to {filename}")
    
    # Create MAT structure  
    mat_2025 = create_mat_structure(data, 2025)
    if mat_2025 is not None:
        filename = "MAT_2025.csv"
        mat_2025.to_csv(filename, index=False, sep=';', encoding='utf-8-sig')
        print(f"✓ Saved MAT format to {filename}")
    
    print(f"\nTransformation completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nGenerated files:")
    for file in ['BI_NOTAS_2025_STG_Ucampus.csv', 'MAT_2025.csv']:
        if os.path.exists(file):
            print(f"  - {file}")

if __name__ == "__main__":
    main()
