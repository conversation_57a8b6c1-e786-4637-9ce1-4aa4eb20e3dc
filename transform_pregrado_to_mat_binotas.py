#!/usr/bin/env python3
"""
Transform pregrado API data (2019-2025) into MAT and BI_NOTAS structures.
Creates tables with date suffix matching existing structure.
"""

import pandas as pd
import pyodbc
import os
import time
from datetime import datetime
from sqlalchemy import create_engine
import urllib.parse

# Database connection parameters
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

CURRENT_DATE = datetime.now().strftime("%d%m%Y")

def wait_for_vpn_confirmation():
    """Wait for user to confirm VPN is on"""
    print("🔒 VPN CONFIRMATION REQUIRED")
    print("="*50)
    print("⚠️  IMPORTANT: You need to turn on your VPN before proceeding!")
    print("📡 This script will connect to SQL Server at **************")
    print("")
    
    while True:
        response = input("✅ Is your VPN connected? (yes/no): ").lower().strip()
        if response in ['yes', 'y', 'si', 'sí']:
            print("🚀 Proceeding with database import...")
            break
        elif response in ['no', 'n']:
            print("⏸️  Please turn on your VPN and run this script again.")
            exit(0)
        else:
            print("❌ Please answer 'yes' or 'no'")

def load_api_data():
    """Load API data from CSV files"""
    print("📖 Loading API data...")
    
    data_dir = f"api_data_pregrado_{CURRENT_DATE}"
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        print("🔄 Please run fetch_pregrado_data_2019_2025.py first")
        return None
    
    data = {}
    
    # Load each dataset
    files = {
        'carreras': f"{data_dir}/carreras_pregrado_{CURRENT_DATE}.csv",
        'carreras_alumnos': f"{data_dir}/carreras_alumnos_{CURRENT_DATE}.csv",
        'planes': f"{data_dir}/planes_pregrado_{CURRENT_DATE}.csv",
        'ramos': f"{data_dir}/ramos_{CURRENT_DATE}.csv",
        'cursos_inscritos': f"{data_dir}/cursos_inscritos_2019_2025_{CURRENT_DATE}.csv",
        'cursos': f"{data_dir}/cursos_2019_2025_{CURRENT_DATE}.csv",
        'instituciones': f"{data_dir}/instituciones_{CURRENT_DATE}.csv"
    }
    
    for key, filepath in files.items():
        if os.path.exists(filepath):
            try:
                df = pd.read_csv(filepath, encoding='utf-8-sig')
                data[key] = df
                print(f"  ✅ {key}: {len(df)} records")
            except Exception as e:
                print(f"  ❌ Error loading {key}: {e}")
                data[key] = pd.DataFrame()
        else:
            print(f"  ⚠️ File not found: {filepath}")
            data[key] = pd.DataFrame()
    
    return data

def create_mat_structure(data):
    """Create MAT structure from API data"""
    print("\n🏗️ Creating MAT structure...")

    # Start with carreras_alumnos to get student-career relationships
    carreras_alumnos = data['carreras_alumnos']
    carreras = data['carreras']

    if carreras_alumnos.empty:
        print("❌ No carreras_alumnos data available")
        return pd.DataFrame()

    # Get unique students per year (matriculation data)
    print("📊 Processing student matriculations...")

    # Extract year from periodo_inicio (e.g., "2019.1" -> 2019)
    carreras_alumnos['year'] = carreras_alumnos['periodo_inicio'].astype(str).str.split('.').str[0].astype(int)

    # Filter for years 2019-2025
    carreras_alumnos = carreras_alumnos[carreras_alumnos['year'].between(2019, 2025)]

    # Get unique student-year-career combinations
    mat_base = carreras_alumnos.copy()
    
    print(f"  📋 Found {len(mat_base)} unique student-year matriculations")
    
    # Create MAT structure matching your existing table
    mat_transformed = pd.DataFrame()
    
    # Basic student information
    mat_transformed['TIPO_DOC'] = 1  # Default document type
    mat_transformed['N_DOC'] = mat_base['rut']

    # Calculate DV (check digit) from RUT if not available
    def calculate_dv(rut):
        """Calculate Chilean RUT check digit"""
        try:
            rut = int(rut)
            suma = 0
            multiplicador = 2
            while rut > 0:
                suma += (rut % 10) * multiplicador
                rut //= 10
                multiplicador += 1
                if multiplicador > 7:
                    multiplicador = 2
            resto = suma % 11
            if resto == 0:
                return '0'
            elif resto == 1:
                return 'K'
            else:
                return str(11 - resto)
        except:
            return '0'

    mat_transformed['DV'] = mat_base['rut'].apply(calculate_dv)

    # Names (use placeholders if not available)
    mat_transformed['PRIMER_APELLIDO'] = mat_base.get('apellido1', pd.Series([''] * len(mat_base))).fillna('')
    mat_transformed['SEGUNDO_APELLIDO'] = mat_base.get('apellido2', pd.Series([''] * len(mat_base))).fillna('')
    mat_transformed['NOMBRE'] = mat_base.get('nombre1', pd.Series([''] * len(mat_base))).fillna('')

    # Demographics (use defaults if not available)
    mat_transformed['SEXO'] = mat_base.get('sexo', pd.Series([''] * len(mat_base))).fillna('')
    mat_transformed['FECH_NAC'] = mat_base.get('fecha_nacimiento', pd.Series([''] * len(mat_base))).fillna('')
    mat_transformed['NAC'] = mat_base.get('nacionalidad', pd.Series(['CL'] * len(mat_base))).fillna('CL')
    
    # Academic information - CORRECT ORDER: FOR_ING_ACT, ANIO_ING_ACT, SEM_ING_ACT, ANIO_ING_ORI, SEM_ING_ORI
    mat_transformed['FOR_ING_ACT'] = 10  # Default form of entry
    mat_transformed['ANIO_ING_ACT'] = mat_base['year']
    mat_transformed['SEM_ING_ACT'] = 1  # Default semester
    mat_transformed['ANIO_ING_ORI'] = mat_base['year']
    mat_transformed['SEM_ING_ORI'] = 1  # Default semester
    
    # Status
    mat_transformed['VIG'] = 1  # Active
    mat_transformed['AÑO_PROCESO'] = mat_base['year']
    
    # Career information (from carreras_alumnos)
    mat_transformed['COD_SIES'] = mat_base['id_carrera'].fillna('')
    mat_transformed['NOMBRE_CARRERA'] = mat_base['nombre'].fillna('')
    mat_transformed['FACULTAD'] = ''  # Will be filled from carreras if available

    # Keys
    mat_transformed['LLAVE_SIES'] = mat_base['id_carrera'].astype(str) + '-' + mat_base['year'].astype(str)
    mat_transformed['COHORTE_ESTUDIANTE'] = mat_base['year']
    mat_transformed['LLAVE_RUT_AÑO'] = mat_base['rut'].astype(str) + '-' + mat_base['year'].astype(str)
    mat_transformed['LLAVE_RUT_COHORTE'] = mat_base['rut'].astype(str) + '-' + mat_base['year'].astype(str)

    # Program information (from carreras_alumnos)
    mat_transformed['TIPO_PLAN'] = mat_base['tipo_titulo_texto'].fillna('')
    mat_transformed['NIVEL'] = mat_base['tipo_titulo_texto'].fillna('')
    mat_transformed['ÁREA DEL CONOCIMIENTO'] = ''
    mat_transformed['CLASIFICACION_NIVEL_1'] = mat_base['tipo_titulo_texto'].fillna('')
    mat_transformed['CLASIFICACION_NIVEL_2'] = ''

    # Try to merge with carreras data to get faculty info
    if not carreras.empty:
        try:
            carreras_subset = carreras[['id_carrera', 'facultad']].drop_duplicates('id_carrera')

            mat_with_carreras = mat_transformed.merge(
                carreras_subset,
                left_on='COD_SIES',
                right_on='id_carrera',
                how='left'
            )

            mat_transformed['FACULTAD'] = mat_with_carreras['facultad'].fillna('')
        except Exception as e:
            print(f"  ⚠️ Could not merge faculty info: {e}")
            mat_transformed['FACULTAD'] = ''
    
    print(f"✅ Created MAT structure with {len(mat_transformed)} records")
    return mat_transformed

def create_bi_notas_structure(data):
    """Create BI_NOTAS structure from API data"""
    print("\n🏗️ Creating BI_NOTAS structure...")
    
    cursos_inscritos = data['cursos_inscritos']
    ramos = data['ramos']
    
    if cursos_inscritos.empty:
        print("❌ No cursos_inscritos data available")
        return pd.DataFrame()
    
    print(f"📊 Processing {len(cursos_inscritos)} course enrollments...")
    
    # Extract year from periodo
    cursos_inscritos['year'] = cursos_inscritos['periodo'].astype(str).str.split('.').str[0].astype(int)

    # Extract semester, handling NaN values
    semester_parts = cursos_inscritos['periodo'].astype(str).str.split('.').str[1]
    cursos_inscritos['semester'] = pd.to_numeric(semester_parts, errors='coerce').fillna(1).astype(int)
    
    # Create BI_NOTAS structure
    bi_notas = pd.DataFrame()
    
    # Main key
    bi_notas['llave'] = (cursos_inscritos['rut'].astype(str) + '-' + 
                        cursos_inscritos['id_curso'].astype(str) + '-' + 
                        cursos_inscritos['codigo'].astype(str) + '-' + 
                        cursos_inscritos['year'].astype(str))
    
    # Year and semester
    bi_notas['annio'] = cursos_inscritos['year']
    bi_notas['semestre'] = cursos_inscritos['semester']
    
    # Student status (use defaults since not available in cursos_inscritos)
    bi_notas['estado'] = 'Activo'  # Default
    bi_notas['annio_ingreso'] = cursos_inscritos['year']  # Approximate
    bi_notas['tipo_ingreso'] = 'Ingreso Regular'  # Default
    
    # Student information
    bi_notas['rut'] = cursos_inscritos['rut']

    # Calculate DV from RUT
    def calculate_dv(rut):
        """Calculate Chilean RUT check digit"""
        try:
            rut = int(rut)
            suma = 0
            multiplicador = 2
            while rut > 0:
                suma += (rut % 10) * multiplicador
                rut //= 10
                multiplicador += 1
                if multiplicador > 7:
                    multiplicador = 2
            resto = suma % 11
            if resto == 0:
                return '0'
            elif resto == 1:
                return 'K'
            else:
                return str(11 - resto)
        except:
            return '0'

    bi_notas['dv'] = cursos_inscritos['rut'].apply(calculate_dv)

    # Names (use placeholders if not available)
    bi_notas['nombres'] = cursos_inscritos.get('nombre1', pd.Series([''] * len(cursos_inscritos))).fillna('')
    bi_notas['apellidos'] = cursos_inscritos.get('apellido1', pd.Series([''] * len(cursos_inscritos))).fillna('')
    
    # Course information
    bi_notas['id_curso'] = cursos_inscritos['id_curso']
    bi_notas['codigo'] = cursos_inscritos['codigo']
    bi_notas['nombre'] = cursos_inscritos['nombre']
    bi_notas['estado_curso'] = cursos_inscritos['estado_curso'].fillna('Aceptado')
    
    # Grades and status
    bi_notas['nota'] = cursos_inscritos['nota_final'].fillna(0.0)
    bi_notas['estado_final'] = cursos_inscritos['estado_final'].fillna('Inscrito')
    
    # SCT credits
    bi_notas['sct'] = cursos_inscritos['sct'].fillna(0)
    
    # Status flags (based on estado_final)
    bi_notas['Inscrito'] = (bi_notas['estado_final'] == 'Inscrito').astype(int)
    bi_notas['Eliminado'] = (bi_notas['estado_final'] == 'Eliminado').astype(int)
    bi_notas['Aprobado'] = (bi_notas['estado_final'] == 'Aprobado').astype(int)
    bi_notas['Reprobado'] = (bi_notas['estado_final'] == 'Reprobado').astype(int)
    bi_notas['N'] = 1  # Always 1 for counting
    
    # Department (use default if not available)
    bi_notas['departamento'] = cursos_inscritos.get('departamento', pd.Series(['Sin Información'] * len(cursos_inscritos))).fillna('Sin Información')
    
    # Additional fields
    bi_notas['codigo_ asignatura_2'] = None
    bi_notas['comentario'] = None
    
    print(f"✅ Created BI_NOTAS structure with {len(bi_notas)} records")
    return bi_notas

def get_max_id_from_table(table_name, id_column='ID'):
    """Get the maximum ID from existing table to continue numbering"""
    try:
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        query = f"SELECT ISNULL(MAX([{id_column}]), 0) as max_id FROM [{table_name}]"
        cursor.execute(query)
        result = cursor.fetchone()
        max_id = int(result[0]) if result and result[0] is not None else 0
        
        conn.close()
        print(f"✓ Maximum ID in {table_name}: {max_id}")
        return max_id
        
    except Exception as e:
        print(f"✗ Error getting max ID from {table_name}: {e}")
        return 0

def main():
    """Main function to transform and import data"""
    print("🔄 TRANSFORMING PREGRADO DATA TO MAT & BI_NOTAS")
    print("="*60)
    print(f"📅 Date suffix: {CURRENT_DATE}")
    print("="*60)
    
    # Wait for VPN confirmation
    wait_for_vpn_confirmation()
    
    # Load API data
    data = load_api_data()
    if not data:
        return
    
    # Create MAT structure
    mat_data = create_mat_structure(data)
    if not mat_data.empty:
        # Add ID column starting from max existing ID
        max_id = get_max_id_from_table('MAT_07_24', 'ID')
        mat_data['ID'] = range(max_id + 1, max_id + 1 + len(mat_data))
        
        # Reorder columns to match existing structure
        mat_columns = ['ID', 'TIPO_DOC', 'N_DOC', 'DV', 'PRIMER_APELLIDO', 'SEGUNDO_APELLIDO', 'NOMBRE', 
                      'SEXO', 'FECH_NAC', 'NAC', 'FOR_ING_ACT', 'ANIO_ING_ACT', 'SEM_ING_ACT', 
                      'ANIO_ING_ORI', 'SEM_ING_ORI', 'VIG', 'AÑO_PROCESO', 'COD_SIES', 'NOMBRE_CARRERA', 
                      'FACULTAD', 'LLAVE_SIES', 'COHORTE_ESTUDIANTE', 'LLAVE_RUT_AÑO', 'LLAVE_RUT_COHORTE',
                      'TIPO_PLAN', 'NIVEL', 'ÁREA DEL CONOCIMIENTO', 'CLASIFICACION_NIVEL_1', 'CLASIFICACION_NIVEL_2']
        
        mat_data = mat_data[mat_columns]
        
        # Save to CSV
        mat_filename = f"MAT_2019_2025_{CURRENT_DATE}.csv"
        mat_data.to_csv(mat_filename, index=False, sep=';', encoding='utf-8-sig')
        print(f"💾 Saved MAT data to {mat_filename}")
    
    # Create BI_NOTAS structure
    bi_notas_data = create_bi_notas_structure(data)
    if not bi_notas_data.empty:
        # Save to CSV
        bi_notas_filename = f"BI_NOTAS_2019_2025_STG_Ucampus_{CURRENT_DATE}.csv"
        bi_notas_data.to_csv(bi_notas_filename, index=False, sep=';', encoding='utf-8-sig')
        print(f"💾 Saved BI_NOTAS data to {bi_notas_filename}")
    
    print(f"\n✅ TRANSFORMATION COMPLETE!")
    print(f"📊 MAT records: {len(mat_data) if not mat_data.empty else 0}")
    print(f"📊 BI_NOTAS records: {len(bi_notas_data) if not bi_notas_data.empty else 0}")
    print(f"\n📋 Next steps:")
    print(f"1. Review the CSV files")
    print(f"2. Use insert_2025_data_to_sql.py to import to database")
    print(f"3. Compare with existing data to verify completeness")

if __name__ == "__main__":
    main()
