#!/usr/bin/env python3
"""
Insert 2025 data into existing SQL Server tables.
- MAT_2025.csv → MAT_07_24 table (with ID continuation)
- BI_NOTAS_2025_STG_Ucampus.csv → BI_NOTAS_07012025_STG_Ucampus table
"""

import pandas as pd
import pyodbc
import os
import time
from tqdm import tqdm
from sqlalchemy import create_engine, text
import urllib.parse

# Database connection parameters - UPDATE THESE WITH CURRENT CREDENTIALS
server = '146.83.250.194'
database = 'analisis_pro'
username = 'dmeza'  # UPDATE IF NEEDED
password = 'L3FGJ#90$#LGA'  # UPDATE WITH CURRENT PASSWORD

# Alternative: You can also set these as environment variables for security
# import os
# username = os.getenv('SQL_USERNAME', 'dmeza')
# password = os.getenv('SQL_PASSWORD', 'your_password_here')

def get_max_id_from_table(table_name, id_column='ID'):
    """Get the maximum ID from existing table to continue numbering."""
    try:
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        query = f"SELECT ISNULL(MAX([{id_column}]), 0) as max_id FROM [{table_name}]"
        cursor.execute(query)
        result = cursor.fetchone()
        max_id = int(result[0]) if result and result[0] is not None else 0

        conn.close()
        print(f"✓ Maximum ID in {table_name}: {max_id}")
        return max_id
        
    except Exception as e:
        print(f"✗ Error getting max ID from {table_name}: {e}")
        return 0

def create_new_mat_table():
    """Create new MAT_2019_2025_25092025 table and insert data."""
    print("\n" + "="*60)
    print("CREATING NEW MAT_2019_2025_25092025 TABLE")
    print("="*60)

    # Check if file exists
    mat_file = "MAT_2019_2025_25092025.csv"
    if not os.path.exists(mat_file):
        print(f"✗ File not found: {mat_file}")
        return False

    # Load the CSV
    print(f"📖 Loading {mat_file}...")
    df = pd.read_csv(mat_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    
    # Get maximum ID from existing table
    target_table = "MAT_07_24"
    max_id = get_max_id_from_table(target_table, 'ID')
    
    # Update IDs to continue from max_id
    print(f"🔢 Updating IDs to start from {max_id + 1}...")
    df['ID'] = range(max_id + 1, max_id + 1 + len(df))
    print(f"✓ New ID range: {int(df['ID'].min())} to {int(df['ID'].max())}")
    
    # Create SQLAlchemy engine for bulk insert
    connection_string_sqlalchemy = (
        f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
        f"?driver=ODBC+Driver+17+for+SQL+Server&charset=utf8"
    )
    
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    # Insert data
    print(f"🚀 Inserting {len(df)} records into {target_table}...")
    start_time = time.time()
    
    try:
        # Clean data before insertion
        df = df.where(pd.notnull(df), None)
        
        # Insert in chunks with progress bar
        chunksize = 1000
        total_chunks = (len(df) + chunksize - 1) // chunksize
        
        with tqdm(total=len(df), desc="Inserting MAT data", unit="rows") as pbar:
            for i in range(0, len(df), chunksize):
                chunk = df.iloc[i:i+chunksize]
                chunk.to_sql(
                    name=target_table,
                    con=engine,
                    if_exists='append',
                    index=False,
                    method=None  # Use default method instead of 'multi'
                )
                pbar.update(len(chunk))
        
        insert_time = time.time() - start_time
        print(f"✅ Successfully inserted {len(df)} records into {target_table}")
        print(f"⏱️  Time: {insert_time:.2f}s ({int(len(df)/insert_time)} rows/sec)")
        return True
        
    except Exception as e:
        print(f"✗ Error inserting MAT data: {e}")
        return False
    finally:
        engine.dispose()

def insert_bi_notas_2025_data():
    """Insert BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv data into BI_NOTAS_07012025_STG_Ucampus table."""
    print("\n" + "="*60)
    print("INSERTING BI_NOTAS 2019-2025 DATA")
    print("="*60)

    # Check if file exists
    bi_file = "BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv"
    if not os.path.exists(bi_file):
        print(f"✗ File not found: {bi_file}")
        return False

    # Load the CSV
    print(f"📖 Loading {bi_file}...")
    df = pd.read_csv(bi_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    
    # Create SQLAlchemy engine for bulk insert
    connection_string_sqlalchemy = (
        f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
        f"?driver=ODBC+Driver+17+for+SQL+Server&charset=utf8"
    )
    
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    # Insert data
    target_table = "BI_NOTAS_07012025_STG_Ucampus"
    print(f"🚀 Inserting {len(df)} records into {target_table}...")
    start_time = time.time()
    
    try:
        # Clean data before insertion
        df = df.where(pd.notnull(df), None)
        
        # Insert in chunks with progress bar
        chunksize = 1000
        
        with tqdm(total=len(df), desc="Inserting BI_NOTAS data", unit="rows") as pbar:
            for i in range(0, len(df), chunksize):
                chunk = df.iloc[i:i+chunksize]
                chunk.to_sql(
                    name=target_table,
                    con=engine,
                    if_exists='append',
                    index=False,
                    method=None  # Use default method instead of 'multi'
                )
                pbar.update(len(chunk))
        
        insert_time = time.time() - start_time
        print(f"✅ Successfully inserted {len(df)} records into {target_table}")
        print(f"⏱️  Time: {insert_time:.2f}s ({int(len(df)/insert_time)} rows/sec)")
        return True
        
    except Exception as e:
        print(f"✗ Error inserting BI_NOTAS data: {e}")
        return False
    finally:
        engine.dispose()

def verify_insertions():
    """Verify that the data was inserted correctly."""
    print("\n" + "="*60)
    print("VERIFICATION")
    print("="*60)
    
    try:
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Check MAT_07_24 table for 2019-2025 data
        cursor.execute("SELECT COUNT(*) FROM [MAT_07_24] WHERE [AÑO_PROCESO] BETWEEN 2019 AND 2025")
        mat_count = cursor.fetchone()[0]
        print(f"📊 MAT_07_24 records with AÑO_PROCESO 2019-2025: {mat_count}")

        # Check BI_NOTAS table for 2019-2025 data
        cursor.execute("SELECT COUNT(*) FROM [BI_NOTAS_07012025_STG_Ucampus] WHERE [annio] BETWEEN 2019 AND 2025")
        bi_count = cursor.fetchone()[0]
        print(f"📊 BI_NOTAS records with annio 2019-2025: {bi_count}")
        
        # Check LLAVE_RUT_AÑO connections
        cursor.execute("""
            SELECT COUNT(DISTINCT m.[LLAVE_RUT_AÑO]) 
            FROM [MAT_07_24] m 
            INNER JOIN [BI_NOTAS_07012025_STG_Ucampus] b 
            ON m.[LLAVE_RUT_AÑO] = b.[LLAVE_RUT_AÑO] 
            WHERE m.[AÑO_PROCESO] = 2025 AND b.[annio] = 2025
        """)
        connected_students = cursor.fetchone()[0]
        print(f"🔗 Students with data in both tables (2025): {connected_students:,}")
        
        # Sample data check
        print("\n📋 Sample 2025 data from MAT_07_24:")
        cursor.execute("""
            SELECT TOP 3 [ID], [N_DOC], [NOMBRE], [PRIMER_APELLIDO], [NOMBRE_CARRERA], [LLAVE_RUT_AÑO]
            FROM [MAT_07_24] 
            WHERE [AÑO_PROCESO] = 2025
            ORDER BY [ID] DESC
        """)
        for row in cursor.fetchall():
            print(f"  ID: {row[0]}, RUT: {row[1]}, Name: {row[2]} {row[3]}, Career: {row[4]}, Key: {row[5]}")
        
        print("\n📋 Sample 2025 data from BI_NOTAS:")
        cursor.execute("""
            SELECT TOP 3 [rut], [nombres], [apellidos], [codigo], [nombre], [nota], [estado_final], [LLAVE_RUT_AÑO]
            FROM [BI_NOTAS_07012025_STG_Ucampus] 
            WHERE [annio] = 2025
        """)
        for row in cursor.fetchall():
            print(f"  RUT: {row[0]}, Name: {row[1]} {row[2]}, Course: {row[3]} - {row[4]}, Grade: {row[5]}, Status: {row[6]}, Key: {row[7]}")
        
        conn.close()
        
        if mat_count > 0 and bi_count > 0 and connected_students > 0:
            print(f"\n✅ Verification successful! Data properly inserted and connected.")
            return True
        else:
            print(f"\n⚠️  Verification issues detected.")
            return False
            
    except Exception as e:
        print(f"✗ Error during verification: {e}")
        return False

def main():
    """Main function to insert 2019-2025 pregrado data into SQL Server tables."""
    print("🚀 INSERTING PREGRADO DATA 2019-2025 INTO SQL SERVER")
    print("="*60)
    print(f"📊 Target Server: {server}")
    print(f"📊 Target Database: {database}")
    print(f"📊 Files to process:")
    print(f"   • MAT_2019_2025_25092025.csv → MAT_07_24 table")
    print(f"   • BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv → BI_NOTAS_07012025_STG_Ucampus table")
    
    start_time = time.time()
    
    # Insert MAT data
    mat_success = insert_mat_2025_data()

    # Insert BI_NOTAS data
    bi_success = insert_bi_notas_2025_data()
    
    # Verify insertions
    verification_success = verify_insertions()
    
    total_time = time.time() - start_time
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    print(f"MAT data insertion: {'✅ SUCCESS' if mat_success else '❌ FAILED'}")
    print(f"BI_NOTAS data insertion: {'✅ SUCCESS' if bi_success else '❌ FAILED'}")
    print(f"Data verification: {'✅ SUCCESS' if verification_success else '❌ FAILED'}")
    print(f"⏱️  Total time: {total_time:.2f}s")
    
    if mat_success and bi_success and verification_success:
        print("\n🎉 ALL OPERATIONS COMPLETED SUCCESSFULLY!")
        print("📊 Your 2025 data is now available in SQL Server for Power BI analysis.")
        print("🔗 Both tables are connected via LLAVE_RUT_AÑO for relationship analysis.")
    else:
        print("\n⚠️  Some operations failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
