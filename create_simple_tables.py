#!/usr/bin/env python3
"""
Simple script to create new tables using the same approach as the working insert script.
"""

import pandas as pd
import pyodbc
import time
import os
from sqlalchemy import create_engine
import urllib.parse
from tqdm import tqdm

# Database connection parameters (updated with current credentials)
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

# Connection strings
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
connection_string_sqlalchemy = (
    f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
    f"?driver=ODBC+Driver+17+for+SQL+Server"
)

def create_mat_table():
    """Create MAT_2019_2025_25092025 table using the same method as working script."""
    print("\n" + "="*60)
    print("CREATING MAT_2019_2025_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    mat_file = "MAT_2019_2025_25092025.csv"
    if not os.path.exists(mat_file):
        print(f"❌ File not found: {mat_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {mat_file}...")
    df = pd.read_csv(mat_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    print(f"✓ ID range: {df['ID'].min()} to {df['ID'].max()}")
    
    # Create SQLAlchemy engine
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    try:
        # Insert data using to_sql - this will create the table automatically
        table_name = "MAT_2019_2025_25092025"
        print(f"🚀 Creating table and inserting {len(df)} records...")
        
        # Insert in chunks with progress bar
        chunk_size = 1000
        total_chunks = len(df) // chunk_size + (1 if len(df) % chunk_size else 0)
        
        with tqdm(total=len(df), desc="Inserting MAT data", unit="rows") as pbar:
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                if i == 0:
                    # First chunk creates the table
                    chunk.to_sql(table_name, engine, if_exists='replace', index=False, method='multi')
                else:
                    # Subsequent chunks append to the table
                    chunk.to_sql(table_name, engine, if_exists='append', index=False, method='multi')
                pbar.update(len(chunk))
        
        print(f"✅ Successfully created table {table_name} with {len(df)} records")
        return True
        
    except Exception as e:
        print(f"❌ Error creating MAT table: {e}")
        return False
    finally:
        engine.dispose()

def create_bi_notas_table():
    """Create BI_NOTAS_2019_2025_STG_Ucampus_25092025 table using the same method as working script."""
    print("\n" + "="*60)
    print("CREATING BI_NOTAS_2019_2025_STG_Ucampus_25092025 TABLE")
    print("="*60)
    
    # Check if file exists
    bi_file = "BI_NOTAS_2019_2025_STG_Ucampus_25092025.csv"
    if not os.path.exists(bi_file):
        print(f"❌ File not found: {bi_file}")
        return False
    
    # Load the CSV
    print(f"📖 Loading {bi_file}...")
    df = pd.read_csv(bi_file, sep=';', encoding='utf-8-sig')
    print(f"✓ Loaded {len(df)} records")
    
    # Create SQLAlchemy engine
    engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
    
    try:
        # Insert data using to_sql - this will create the table automatically
        table_name = "BI_NOTAS_2019_2025_STG_Ucampus_25092025"
        print(f"🚀 Creating table and inserting {len(df)} records...")
        
        # Insert in chunks with progress bar
        chunk_size = 1000
        
        with tqdm(total=len(df), desc="Inserting BI_NOTAS data", unit="rows") as pbar:
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                if i == 0:
                    # First chunk creates the table
                    chunk.to_sql(table_name, engine, if_exists='replace', index=False, method='multi')
                else:
                    # Subsequent chunks append to the table
                    chunk.to_sql(table_name, engine, if_exists='append', index=False, method='multi')
                pbar.update(len(chunk))
        
        print(f"✅ Successfully created table {table_name} with {len(df)} records")
        return True
        
    except Exception as e:
        print(f"❌ Error creating BI_NOTAS table: {e}")
        return False
    finally:
        engine.dispose()

def verify_tables():
    """Verify the new tables were created successfully."""
    print("\n" + "="*60)
    print("VERIFICATION")
    print("="*60)
    
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Check MAT table
        cursor.execute("SELECT COUNT(*) FROM [MAT_2019_2025_25092025]")
        mat_count = cursor.fetchone()[0]
        print(f"📊 MAT_2019_2025_25092025 records: {mat_count:,}")
        
        # Check BI_NOTAS table
        cursor.execute("SELECT COUNT(*) FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025]")
        bi_count = cursor.fetchone()[0]
        print(f"📊 BI_NOTAS_2019_2025_STG_Ucampus_25092025 records: {bi_count:,}")
        
        # Show year breakdown for BI_NOTAS
        if bi_count > 0:
            print(f"\n📋 BI_NOTAS breakdown by year:")
            cursor.execute("""
                SELECT [annio], COUNT(*) as count 
                FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025] 
                GROUP BY [annio] 
                ORDER BY [annio]
            """)
            for row in cursor.fetchall():
                print(f"  {row[0]}: {row[1]:,} records")
        
        # Quick FOBI13 check
        if bi_count > 0:
            print(f"\n🔍 FOBI13 Quick Check:")
            cursor.execute("""
                SELECT [annio], COUNT(*) as total, 
                       SUM(CASE WHEN [estado_final] = 'Aprobado' THEN 1 ELSE 0 END) as aprobados
                FROM [BI_NOTAS_2019_2025_STG_Ucampus_25092025] 
                WHERE [codigo] = 'FOBI13'
                GROUP BY [annio] 
                ORDER BY [annio]
            """)
            fobi13_data = cursor.fetchall()
            if fobi13_data:
                for row in fobi13_data:
                    year, total, aprobados = row
                    tasa = (aprobados/total*100) if total > 0 else 0
                    print(f"  {year}: {total} students, {aprobados} approved, {tasa:.1f}% approval rate")
            else:
                print("  No FOBI13 data found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function."""
    print("🚀 CREATING NEW TABLES FOR 2019-2025 PREGRADO DATA")
    print("="*60)
    print(f"📊 Target Server: {server}")
    print(f"📊 Target Database: {database}")
    print(f"📊 New tables to create:")
    print(f"   • MAT_2019_2025_25092025")
    print(f"   • BI_NOTAS_2019_2025_STG_Ucampus_25092025")
    
    start_time = time.time()
    
    # Create new MAT table
    mat_success = create_mat_table()
    
    # Create new BI_NOTAS table
    bi_success = create_bi_notas_table()
    
    # Verify new tables
    verify_success = verify_tables()
    
    # Final summary
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    print(f"MAT table creation: {'✅ SUCCESS' if mat_success else '❌ FAILED'}")
    print(f"BI_NOTAS table creation: {'✅ SUCCESS' if bi_success else '❌ FAILED'}")
    print(f"Verification: {'✅ SUCCESS' if verify_success else '❌ FAILED'}")
    print(f"⏱️  Total time: {total_time:.2f}s")
    
    if all([mat_success, bi_success, verify_success]):
        print("\n🎉 All operations completed successfully!")
        print("\n📋 Next steps:")
        print("   • Use MAT_2019_2025_25092025 for matriculation data")
        print("   • Use BI_NOTAS_2019_2025_STG_Ucampus_25092025 for grades data")
        print("   • Check FOBI13 course data in the new BI_NOTAS table")
        print("   • Update your Power BI reports to use the new tables")
    else:
        print("\n⚠️  Some operations failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
