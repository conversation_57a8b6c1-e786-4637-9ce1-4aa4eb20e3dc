@echo off
echo ============================================================
echo COMPLETE 2025 DATA PIPELINE - API TO BI FORMAT
echo ============================================================

echo Installing required packages...
pip install -r requirements.txt

echo.
echo ============================================================
echo STEP 1: Fetching main API data...
echo ============================================================
python fetch_api_data_2025.py

echo.
echo ============================================================
echo STEP 2: Fetching specific endpoints (malla, avance_curricular)...
echo ============================================================
python fetch_specific_endpoints.py

echo.
echo ============================================================
echo STEP 3: Transforming to BI_NOTAS and MAT format...
echo ============================================================
python transform_api_to_bi_format.py

echo.
echo ============================================================
echo PIPELINE COMPLETED!
echo ============================================================
echo Generated files:
echo   - BI_NOTAS_2025_STG_Ucampus.csv (matches your BI_NOTAS structure)
echo   - MAT_2025.csv (matches your MAT structure)
echo   - Both connected via LLAVE_RUT_AÑO key
echo.
echo Press any key to exit...
pause
