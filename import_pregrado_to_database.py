#!/usr/bin/env python3
"""
Import pregrado data (2019-2025) to SQL Server database.
Creates tables with date suffix. WAITS for user confirmation before executing.
"""

import pandas as pd
import pyodbc
import os
import time
from datetime import datetime
from sqlalchemy import create_engine
import urllib.parse

# Database connection parameters
server = '**************'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

CURRENT_DATE = datetime.now().strftime("%d%m%Y")

def wait_for_vpn_confirmation():
    """Wait for user to confirm VPN is on"""
    print("🔒 VPN CONFIRMATION REQUIRED")
    print("="*50)
    print("⚠️  IMPORTANT: You need to turn on your VPN before proceeding!")
    print("📡 This script will connect to SQL Server at **************")
    print("")
    
    while True:
        response = input("✅ Is your VPN connected? (yes/no): ").lower().strip()
        if response in ['yes', 'y', 'si', 'sí']:
            print("🚀 Proceeding with database import...")
            break
        elif response in ['no', 'n']:
            print("⏸️  Please turn on your VPN and run this script again.")
            exit(0)
        else:
            print("❌ Please answer 'yes' or 'no'")

def test_database_connection():
    """Test database connection"""
    print("\n🔌 Testing database connection...")
    
    try:
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]
        conn.close()
        
        print("✅ Database connection successful!")
        print(f"📊 SQL Server version: {version[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("🔒 Make sure your VPN is connected!")
        return False

def map_dtype_to_sql(dtype, col_name=None):
    """Map pandas dtype to SQL Server type"""
    if pd.api.types.is_integer_dtype(dtype):
        return "INT"
    elif pd.api.types.is_float_dtype(dtype):
        return "FLOAT"
    elif pd.api.types.is_bool_dtype(dtype):
        return "BIT"
    elif pd.api.types.is_datetime64_any_dtype(dtype):
        return "DATETIME"
    else:
        return "NVARCHAR(MAX)"

def create_table_from_dataframe(df, table_name, conn):
    """Create SQL Server table from DataFrame structure"""
    print(f"🏗️ Creating table: {table_name}")
    
    cursor = conn.cursor()
    
    # Drop table if exists
    drop_query = f"DROP TABLE IF EXISTS [{table_name}]"
    cursor.execute(drop_query)
    
    # Create column definitions
    column_defs = []
    for col in df.columns:
        safe_col = col.replace(' ', '_').replace('-', '_').replace('.', '_')
        sql_type = map_dtype_to_sql(df[col].dtype, col_name=col)
        column_defs.append(f"[{safe_col}] {sql_type} NULL")
    
    # Create table
    create_query = f"""
    CREATE TABLE [{table_name}] (
        {', '.join(column_defs)}
    )
    """
    
    cursor.execute(create_query)
    conn.commit()
    print(f"✅ Table {table_name} created successfully")

def import_csv_to_database(csv_file, table_name):
    """Import CSV file to SQL Server database"""
    print(f"\n📊 Importing {csv_file} to {table_name}...")
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    try:
        # Read CSV
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"📖 Loaded {len(df)} records from {csv_file}")
        
        if len(df) == 0:
            print("⚠️ No data to import")
            return True
        
        # Clean column names
        df.columns = [col.replace(' ', '_').replace('-', '_').replace('.', '_') for col in df.columns]
        
        # Connect to database
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )
        
        conn = pyodbc.connect(connection_string)
        
        # Create table
        create_table_from_dataframe(df, table_name, conn)
        
        # Create SQLAlchemy engine for bulk insert
        connection_string_sqlalchemy = (
            f"mssql+pyodbc://{username}:{urllib.parse.quote_plus(password)}@{server}/{database}"
            f"?driver=ODBC+Driver+17+for+SQL+Server&charset=utf8"
        )
        
        engine = create_engine(connection_string_sqlalchemy, fast_executemany=True)
        
        # Insert data in chunks
        chunksize = 1000
        total_chunks = (len(df) + chunksize - 1) // chunksize
        
        print(f"🚀 Inserting data in {total_chunks} chunks...")
        
        for i in range(0, len(df), chunksize):
            chunk = df.iloc[i:i+chunksize]
            chunk.to_sql(
                name=table_name,
                con=engine,
                if_exists='append',
                index=False,
                method=None
            )
            print(f"  ✓ Inserted chunk {(i//chunksize)+1}/{total_chunks}")
        
        conn.close()
        engine.dispose()
        
        print(f"✅ Successfully imported {len(df)} records to {table_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error importing {csv_file}: {e}")
        return False

def main():
    """Main function to import all pregrado data"""
    print("📊 IMPORTING PREGRADO DATA TO SQL SERVER")
    print("="*60)
    print(f"📅 Date suffix: {CURRENT_DATE}")
    print(f"🎯 Target: Pregrado data 2019-2025")
    print("="*60)
    
    # Wait for VPN confirmation
    wait_for_vpn_confirmation()
    
    # Test database connection
    if not test_database_connection():
        print("❌ Cannot proceed without database connection")
        return
    
    # Find data directory
    data_dir = f"api_data_pregrado_{CURRENT_DATE}"
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        print("🔄 Please run fetch_pregrado_data_2019_2025.py first")
        return
    
    print(f"📁 Data directory: {data_dir}")
    
    # Define files to import and their table names
    files_to_import = [
        # Reference data
        (f"{data_dir}/instituciones_{CURRENT_DATE}.csv", f"INSTITUCIONES_{CURRENT_DATE}"),
        (f"{data_dir}/carreras_pregrado_{CURRENT_DATE}.csv", f"CARRERAS_PREGRADO_{CURRENT_DATE}"),
        (f"{data_dir}/planes_pregrado_{CURRENT_DATE}.csv", f"PLANES_PREGRADO_{CURRENT_DATE}"),
        (f"{data_dir}/ramos_{CURRENT_DATE}.csv", f"RAMOS_{CURRENT_DATE}"),
        
        # Year-specific data
        (f"{data_dir}/cursos_inscritos_2019_2025_{CURRENT_DATE}.csv", f"CURSOS_INSCRITOS_2019_2025_{CURRENT_DATE}"),
        (f"{data_dir}/cursos_2019_2025_{CURRENT_DATE}.csv", f"CURSOS_2019_2025_{CURRENT_DATE}"),
        (f"{data_dir}/situaciones_2019_2025_{CURRENT_DATE}.csv", f"SITUACIONES_2019_2025_{CURRENT_DATE}"),
    ]
    
    # Import each file
    successful_imports = 0
    total_files = len(files_to_import)
    
    for csv_file, table_name in files_to_import:
        if import_csv_to_database(csv_file, table_name):
            successful_imports += 1
        
        # Small delay between imports
        time.sleep(1)
    
    # Summary
    print(f"\n" + "="*60)
    print("IMPORT SUMMARY")
    print("="*60)
    print(f"✅ Successful imports: {successful_imports}/{total_files}")
    print(f"❌ Failed imports: {total_files - successful_imports}")
    
    if successful_imports == total_files:
        print(f"\n🎉 ALL DATA IMPORTED SUCCESSFULLY!")
        print(f"📊 Tables created with suffix: {CURRENT_DATE}")
        print(f"🔍 You can now analyze the pregrado data from 2019-2025")
    else:
        print(f"\n⚠️ Some imports failed. Check the logs above.")
    
    print(f"\n📋 Next steps:")
    print(f"1. Verify data in SQL Server Management Studio")
    print(f"2. Check for DERECHO programs in CARRERAS_PREGRADO_{CURRENT_DATE}")
    print(f"3. Compare with existing database to identify discrepancies")

if __name__ == "__main__":
    main()
