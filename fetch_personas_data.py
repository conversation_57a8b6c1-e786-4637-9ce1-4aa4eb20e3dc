#!/usr/bin/env python3
"""
Fetch personas data for the students in our 2025 dataset.
This will get the real names, surnames, and demographic information.
"""

import requests
import pandas as pd
import json
import os
from datetime import datetime

# API credentials
BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
USERNAME = '48bc01ad5501e88f249831516829efeb'
PASSWORD = '030643a80361e103'

def get_unique_ruts():
    """Get unique RUTs from our existing data files."""
    ruts = set()
    
    # Get RUTs from carreras_alumnos
    carreras_file = "api_data_2025/carreras_alumnos_2025.csv"
    if os.path.exists(carreras_file):
        df = pd.read_csv(carreras_file)
        ruts.update(df['rut'].unique())
        print(f"Found {len(df['rut'].unique())} unique RUTs from carreras_alumnos")
    
    # Get RUTs from cursos_inscritos
    cursos_file = "api_data_2025/cursos_inscritos_2025.csv"
    if os.path.exists(cursos_file):
        df = pd.read_csv(cursos_file)
        ruts.update(df['rut'].unique())
        print(f"Found {len(df['rut'].unique())} unique RUTs from cursos_inscritos")
    
    return list(ruts)

def fetch_personas_batch(ruts_batch):
    """Fetch personas data for a batch of RUTs."""
    url = f"{BASE_URL}/personas"
    
    # Create array parameter for multiple RUTs
    params = {}
    for i, rut in enumerate(ruts_batch):
        params[f'rut[{i}]'] = rut
    
    try:
        response = requests.get(url, auth=(USERNAME, PASSWORD), params=params, timeout=60)
        response.raise_for_status()
        
        data = response.json()
        return data if isinstance(data, list) else [data]
        
    except Exception as e:
        print(f"Error fetching batch: {e}")
        return []

def fetch_all_personas(ruts, batch_size=50):
    """Fetch personas data for all RUTs in batches."""
    print(f"\n--- Fetching personas data for {len(ruts)} RUTs ---")
    
    all_personas = []
    total_batches = (len(ruts) + batch_size - 1) // batch_size
    
    for i in range(0, len(ruts), batch_size):
        batch = ruts[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        
        print(f"Fetching batch {batch_num}/{total_batches} ({len(batch)} RUTs)")
        
        personas_batch = fetch_personas_batch(batch)
        if personas_batch:
            all_personas.extend(personas_batch)
            print(f"  ✓ Got {len(personas_batch)} persona records")
        else:
            print(f"  ✗ No data for batch {batch_num}")
    
    print(f"\n✓ Total personas fetched: {len(all_personas)}")
    return all_personas

def save_personas_data(personas_data):
    """Save personas data to CSV."""
    if not personas_data:
        print("No personas data to save")
        return
    
    # Create output directory if needed
    os.makedirs("api_data_2025", exist_ok=True)
    
    # Save to CSV
    df = pd.DataFrame(personas_data)
    filename = "api_data_2025/personas_2025.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✓ Saved {len(df)} persona records to {filename}")
    
    # Show sample of what we got
    print("\nSample persona data:")
    sample_cols = ['rut', 'nombre1', 'nombre2', 'apellido1', 'apellido2', 'genero']
    available_cols = [col for col in sample_cols if col in df.columns]
    if available_cols:
        print(df[available_cols].head(3).to_string(index=False))

def main():
    """Main function to fetch personas data."""
    print("=" * 60)
    print("PERSONAS DATA FETCHER - 2025")
    print("=" * 60)
    
    # Get unique RUTs from our data
    ruts = get_unique_ruts()
    if not ruts:
        print("No RUTs found in existing data files")
        return
    
    print(f"Total unique RUTs to fetch: {len(ruts)}")
    
    # Fetch personas data
    personas_data = fetch_all_personas(ruts, batch_size=50)
    
    # Save the data
    save_personas_data(personas_data)
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nNext step: Run the transformation script again to get real names!")

if __name__ == "__main__":
    main()
