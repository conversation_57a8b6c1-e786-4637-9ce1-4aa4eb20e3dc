#!/usr/bin/env python3
"""
Analyze FOBI13 - Anatomía Humana General course data from 2019-2023
to investigate the 0% behavior mentioned by the user.
"""

import pandas as pd
import pyodbc
import requests
import json
from datetime import datetime

# Database connection parameters
server = '146.83.250.194'
database = 'analisis_pro'
username = 'dmeza'
password = 'L3FGJ#90$#LGA'

# API credentials
API_BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
API_USERNAME = '48bc01ad5501e88f249831516829efeb'
API_PASSWORD = '030643a80361e103'

def query_database_fobi13():
    """Query the database for FOBI13 course data from 2019-2023"""
    print("🔍 Querying database for FOBI13 course data (2019-2023)...")

    try:
        connection_string = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={server};'
            f'DATABASE={database};'
            f'UID={username};'
            f'PWD={password};'
            f'CHARSET=UTF8'
        )

        conn = pyodbc.connect(connection_string)

        # First, let's check what tables exist and find FOBI13 data
        print("📋 Checking available tables...")
        tables_query = """
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        AND TABLE_NAME LIKE '%NOTAS%'
        ORDER BY TABLE_NAME
        """

        tables_df = pd.read_sql(tables_query, conn)
        print("Available NOTAS tables:")
        for table in tables_df['TABLE_NAME']:
            print(f"  - {table}")

        # Try multiple possible table names
        possible_tables = [
            'BI_NOTAS_07012025_STG_Ucampus',
            'BI_NOTAS_STG_Ucampus',
            'NOTAS',
            'BI_NOTAS'
        ]

        results = []

        for table_name in possible_tables:
            try:
                print(f"\n🔍 Checking table: {table_name}")

                # Check if table exists
                check_query = f"""
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = '{table_name}'
                """

                check_df = pd.read_sql(check_query, conn)
                if check_df['count'].iloc[0] == 0:
                    print(f"  ✗ Table {table_name} does not exist")
                    continue

                # Query FOBI13 data from this table
                query = f"""
                SELECT
                    annio,
                    codigo,
                    nombre,
                    COUNT(*) as total_students,
                    COUNT(CASE WHEN estado_final = 'Aprobado' THEN 1 END) as aprobados,
                    COUNT(CASE WHEN estado_final = 'Reprobado' THEN 1 END) as reprobados,
                    COUNT(CASE WHEN estado_final = 'Inscrito' THEN 1 END) as inscritos,
                    AVG(CASE WHEN nota > 0 THEN nota END) as promedio_nota,
                    MIN(nota) as nota_minima,
                    MAX(nota) as nota_maxima,
                    CAST(COUNT(CASE WHEN estado_final = 'Aprobado' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as tasa_aprobacion,
                    '{table_name}' as source_table
                FROM [{table_name}]
                WHERE codigo = 'FOBI13'
                AND annio BETWEEN 2019 AND 2023
                GROUP BY annio, codigo, nombre
                ORDER BY annio
                """

                df = pd.read_sql(query, conn)
                if len(df) > 0:
                    print(f"  ✓ Found {len(df)} records for FOBI13 in {table_name}")
                    results.append(df)
                else:
                    print(f"  ✗ No FOBI13 data found in {table_name}")

            except Exception as e:
                print(f"  ✗ Error querying {table_name}: {e}")

        conn.close()

        # Combine results
        if results:
            combined_df = pd.concat(results, ignore_index=True)
            print(f"\n✓ Total records found: {len(combined_df)}")
            return combined_df
        else:
            print(f"\n✗ No FOBI13 data found in any table")
            return None

    except Exception as e:
        print(f"✗ Error connecting to database: {e}")
        return None

def query_api_fobi13():
    """Query the API for FOBI13 course data from 2019-2023"""
    print("\n🌐 Querying API for FOBI13 course data (2019-2023)...")
    
    results = {}
    
    for year in range(2019, 2024):  # 2019-2023
        print(f"\n--- Checking year {year} ---")
        
        # Query cursos_inscritos for FOBI13
        try:
            url = f"{API_BASE_URL}/cursos_inscritos"
            params = {'periodo': f'{year}.1,{year}.2'}  # Both semesters
            
            response = requests.get(url, auth=(API_USERNAME, API_PASSWORD), params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            # Filter for FOBI13
            fobi13_data = [record for record in data if record.get('codigo') == 'FOBI13']
            
            if fobi13_data:
                # Calculate statistics
                total_students = len(fobi13_data)
                aprobados = len([r for r in fobi13_data if r.get('estado_final') == 'Aprobado'])
                reprobados = len([r for r in fobi13_data if r.get('estado_final') == 'Reprobado'])
                inscritos = len([r for r in fobi13_data if r.get('estado_final') == 'Inscrito'])
                
                # Calculate grades
                notas = [float(r.get('nota_final', 0)) for r in fobi13_data if r.get('nota_final') and float(r.get('nota_final', 0)) > 0]
                promedio_nota = sum(notas) / len(notas) if notas else 0
                nota_minima = min(notas) if notas else 0
                nota_maxima = max(notas) if notas else 0
                
                tasa_aprobacion = (aprobados * 100.0 / total_students) if total_students > 0 else 0
                
                results[year] = {
                    'annio': year,
                    'codigo': 'FOBI13',
                    'nombre': fobi13_data[0].get('nombre', 'Anatomía Humana General'),
                    'total_students': total_students,
                    'aprobados': aprobados,
                    'reprobados': reprobados,
                    'inscritos': inscritos,
                    'promedio_nota': round(promedio_nota, 2),
                    'nota_minima': nota_minima,
                    'nota_maxima': nota_maxima,
                    'tasa_aprobacion': round(tasa_aprobacion, 2)
                }
                
                print(f"✓ {year}: {total_students} students, {tasa_aprobacion:.1f}% approval rate")
            else:
                print(f"✗ {year}: No FOBI13 data found")
                results[year] = None
                
        except Exception as e:
            print(f"✗ Error fetching {year} data: {e}")
            results[year] = None
    
    return results

def compare_database_vs_api(db_data, api_data):
    """Compare database and API results"""
    print("\n" + "="*80)
    print("COMPARISON: DATABASE vs API")
    print("="*80)
    
    if db_data is not None and len(db_data) > 0:
        print("\n📊 DATABASE RESULTS:")
        print(db_data.to_string(index=False))
    else:
        print("\n📊 DATABASE RESULTS: No data found")
    
    print("\n🌐 API RESULTS:")
    if api_data:
        api_df = pd.DataFrame([data for data in api_data.values() if data is not None])
        if len(api_df) > 0:
            print(api_df.to_string(index=False))
        else:
            print("No data found in API")
    else:
        print("No data found in API")
    
    # Analysis
    print("\n" + "="*80)
    print("ANALYSIS")
    print("="*80)
    
    if db_data is not None and len(db_data) > 0:
        print("\n🔍 DATABASE ANALYSIS:")
        for _, row in db_data.iterrows():
            year = row['annio']
            tasa = row['tasa_aprobacion']
            total = row['total_students']
            print(f"  {year}: {total} students, {tasa:.1f}% approval rate")
            
            if tasa == 0.0 and total > 0:
                print(f"    ⚠️  ANOMALY: 0% approval rate with {total} students!")
    
    if api_data:
        print("\n🌐 API ANALYSIS:")
        for year, data in api_data.items():
            if data:
                tasa = data['tasa_aprobacion']
                total = data['total_students']
                print(f"  {year}: {total} students, {tasa:.1f}% approval rate")
                
                if tasa == 0.0 and total > 0:
                    print(f"    ⚠️  ANOMALY: 0% approval rate with {total} students!")
            else:
                print(f"  {year}: No data")

def main():
    """Main function to analyze FOBI13 course"""
    print("🔬 ANALYZING FOBI13 - Anatomía Humana General (2019-2023)")
    print("="*80)
    
    # Query database
    db_data = query_database_fobi13()
    
    # Query API
    api_data = query_api_fobi13()
    
    # Compare results
    compare_database_vs_api(db_data, api_data)
    
    print("\n✅ Analysis complete!")

if __name__ == "__main__":
    main()
