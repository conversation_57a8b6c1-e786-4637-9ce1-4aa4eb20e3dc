#!/usr/bin/env python3
"""
Script to fetch data from endpoints that require specific parameters.
Run this after running the main fetch script to get plan IDs and student RUTs.
"""

import requests
import pandas as pd
import json
import os
from datetime import datetime

# API credentials
BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
USERNAME = '48bc01ad5501e88f249831516829efeb'
PASSWORD = '030643a80361e103'

def load_csv_data(filename):
    """Load data from CSV file if it exists."""
    if os.path.exists(filename):
        try:
            return pd.read_csv(filename)
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    return None

def fetch_malla_data(plan_ids, year=2025):
    """Fetch malla data for specific plan IDs."""
    print(f"\n--- Fetching malla data for {len(plan_ids)} plans ---")
    all_malla_data = []
    
    for i, plan_id in enumerate(plan_ids[:10]):  # Limit to first 10 for testing
        try:
            print(f"Fetching malla for plan {plan_id} ({i+1}/{min(10, len(plan_ids))})")
            url = f"{BASE_URL}/malla"
            params = {'id_plan': plan_id}
            
            response = requests.get(url, auth=(USERNAME, PASSWORD), params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if isinstance(data, list):
                for record in data:
                    record['id_plan_query'] = plan_id  # Add the queried plan ID
                all_malla_data.extend(data)
            else:
                data['id_plan_query'] = plan_id
                all_malla_data.append(data)
                
            print(f"  ✓ Got {len(data) if isinstance(data, list) else 1} records")
            
        except Exception as e:
            print(f"  ✗ Error fetching malla for plan {plan_id}: {e}")
            continue
    
    return all_malla_data

def fetch_avance_curricular_data(plan_ids, student_ruts, year=2025):
    """Fetch avance_curricular data for specific plan IDs and student RUTs."""
    print(f"\n--- Fetching avance_curricular data ---")
    print(f"Plans: {len(plan_ids)}, Students: {len(student_ruts)}")
    
    all_avance_data = []
    
    # Limit to first few for testing
    limited_plans = plan_ids[:3]
    limited_ruts = student_ruts[:50]
    
    for i, plan_id in enumerate(limited_plans):
        print(f"\nProcessing plan {plan_id} ({i+1}/{len(limited_plans)})")
        
        for j, rut in enumerate(limited_ruts):
            if j % 10 == 0:  # Progress indicator
                print(f"  Processing student {j+1}/{len(limited_ruts)}")
            
            try:
                url = f"{BASE_URL}/avance_curricular"
                params = {
                    'id_plan': plan_id,
                    'rut': rut,
                    'periodo': f'{year}.2'  # Get data up to second semester
                }
                
                response = requests.get(url, auth=(USERNAME, PASSWORD), params=params, timeout=15)
                response.raise_for_status()
                
                data = response.json()
                if isinstance(data, list):
                    for record in data:
                        record['id_plan_query'] = plan_id
                        record['rut_query'] = rut
                    all_avance_data.extend(data)
                else:
                    data['id_plan_query'] = plan_id
                    data['rut_query'] = rut
                    all_avance_data.append(data)
                
            except Exception as e:
                # Don't print every error to avoid spam
                if j < 5:  # Only print first few errors
                    print(f"    Error for plan {plan_id}, rut {rut}: {e}")
                continue
    
    print(f"✓ Collected {len(all_avance_data)} avance_curricular records")
    return all_avance_data

def save_data_to_csv(data, filename):
    """Save data to CSV file."""
    if not data:
        print(f"No data to save for {filename}")
        return
    
    try:
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✓ Saved {len(df)} records to {filename}")
    except Exception as e:
        print(f"✗ Error saving {filename}: {e}")

def main():
    """Main function to fetch specific endpoint data."""
    print("=" * 60)
    print("SPECIFIC ENDPOINTS DATA FETCHER - 2025")
    print("=" * 60)
    
    # Change to the data directory if it exists
    if os.path.exists("api_data_2025"):
        os.chdir("api_data_2025")
        print("Working in api_data_2025 directory")
    
    # Load plan IDs from planes data
    planes_df = load_csv_data("planes_2025.csv")
    if planes_df is not None and 'id_plan' in planes_df.columns:
        plan_ids = planes_df['id_plan'].unique().tolist()
        print(f"Found {len(plan_ids)} unique plan IDs")
    else:
        print("⚠ No planes data found. Run the main fetch script first.")
        plan_ids = []
    
    # Load student RUTs from carreras_alumnos data
    carreras_alumnos_df = load_csv_data("carreras_alumnos_2025.csv")
    if carreras_alumnos_df is not None and 'rut' in carreras_alumnos_df.columns:
        student_ruts = carreras_alumnos_df['rut'].unique().tolist()
        print(f"Found {len(student_ruts)} unique student RUTs")
    else:
        print("⚠ No carreras_alumnos data found. Run the main fetch script first.")
        student_ruts = []
    
    # Fetch malla data
    if plan_ids:
        malla_data = fetch_malla_data(plan_ids)
        save_data_to_csv(malla_data, "malla_2025.csv")
    
    # Fetch avance_curricular data
    if plan_ids and student_ruts:
        avance_data = fetch_avance_curricular_data(plan_ids, student_ruts)
        save_data_to_csv(avance_data, "avance_curricular_2025.csv")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
