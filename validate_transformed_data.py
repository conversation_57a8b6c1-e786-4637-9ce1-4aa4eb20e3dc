#!/usr/bin/env python3
"""
Validate that the transformed 2025 data matches the structure of existing BI files.
"""

import pandas as pd
import os

def compare_structures(original_file, new_file, file_type):
    """Compare the structure of original and new files."""
    print(f"\n--- Validating {file_type} Structure ---")
    
    if not os.path.exists(original_file):
        print(f"⚠ Original file not found: {original_file}")
        return False
    
    if not os.path.exists(new_file):
        print(f"⚠ New file not found: {new_file}")
        return False
    
    try:
        # Load files
        original_df = pd.read_csv(original_file, sep=';', nrows=5)  # Just check structure
        new_df = pd.read_csv(new_file, sep=';', nrows=5)
        
        print(f"Original {file_type} columns ({len(original_df.columns)}):")
        for i, col in enumerate(original_df.columns):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\nNew {file_type} columns ({len(new_df.columns)}):")
        for i, col in enumerate(new_df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # Check if all original columns exist in new file
        missing_cols = set(original_df.columns) - set(new_df.columns)
        extra_cols = set(new_df.columns) - set(original_df.columns)
        
        if missing_cols:
            print(f"\n⚠ Missing columns in new file: {missing_cols}")
        
        if extra_cols:
            print(f"\n✓ Extra columns in new file: {extra_cols}")
        
        if not missing_cols:
            print(f"\n✓ All original columns present in new {file_type} file!")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"✗ Error comparing files: {e}")
        return False

def validate_llave_rut_ano(bi_file, mat_file):
    """Validate LLAVE_RUT_AÑO connection between files."""
    print(f"\n--- Validating LLAVE_RUT_AÑO Connection ---")
    
    try:
        bi_df = pd.read_csv(bi_file, sep=';')
        mat_df = pd.read_csv(mat_file, sep=';')
        
        bi_keys = set(bi_df['LLAVE_RUT_AÑO'].dropna())
        mat_keys = set(mat_df['LLAVE_RUT_AÑO'].dropna())
        
        print(f"BI_NOTAS unique LLAVE_RUT_AÑO: {len(bi_keys)}")
        print(f"MAT unique LLAVE_RUT_AÑO: {len(mat_keys)}")
        
        common_keys = bi_keys.intersection(mat_keys)
        print(f"Common keys: {len(common_keys)}")
        
        if common_keys:
            print("✓ Files can be connected via LLAVE_RUT_AÑO!")
            
            # Show sample keys
            sample_keys = list(common_keys)[:5]
            print(f"Sample common keys: {sample_keys}")
            
            return True
        else:
            print("⚠ No common LLAVE_RUT_AÑO keys found!")
            return False
            
    except Exception as e:
        print(f"✗ Error validating connection: {e}")
        return False

def show_sample_data(filename, file_type):
    """Show sample data from the file."""
    print(f"\n--- Sample {file_type} Data ---")
    
    try:
        df = pd.read_csv(filename, sep=';', nrows=3)
        print(df.to_string(index=False))
        print(f"\nTotal records in file: {len(pd.read_csv(filename, sep=';'))}")
        
    except Exception as e:
        print(f"✗ Error reading sample data: {e}")

def main():
    """Main validation function."""
    print("=" * 60)
    print("DATA VALIDATION - 2025 TRANSFORMED FILES")
    print("=" * 60)
    
    # File paths
    original_bi = "BI_NOTAS_07012025_STG_Ucampus.csv"
    new_bi = "BI_NOTAS_2025_STG_Ucampus.csv"
    original_mat = "MAT_07_24.csv"
    new_mat = "MAT_2025.csv"
    
    # Validate structures
    bi_valid = compare_structures(original_bi, new_bi, "BI_NOTAS")
    mat_valid = compare_structures(original_mat, new_mat, "MAT")
    
    # Validate connection
    if os.path.exists(new_bi) and os.path.exists(new_mat):
        connection_valid = validate_llave_rut_ano(new_bi, new_mat)
    else:
        connection_valid = False
    
    # Show sample data
    if os.path.exists(new_bi):
        show_sample_data(new_bi, "BI_NOTAS 2025")
    
    if os.path.exists(new_mat):
        show_sample_data(new_mat, "MAT 2025")
    
    # Summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    print(f"BI_NOTAS structure: {'✓ VALID' if bi_valid else '✗ ISSUES'}")
    print(f"MAT structure: {'✓ VALID' if mat_valid else '✗ ISSUES'}")
    print(f"LLAVE_RUT_AÑO connection: {'✓ VALID' if connection_valid else '✗ ISSUES'}")
    
    if bi_valid and mat_valid and connection_valid:
        print("\n🎉 All validations passed! Files are ready for Power BI.")
    else:
        print("\n⚠ Some validations failed. Check the issues above.")

if __name__ == "__main__":
    main()
