#!/usr/bin/env python3
"""
Check the exact schema of SQL Server tables to match CSV structure
"""

import pyodbc
import pandas as pd

# Database connection
SERVER = '146.83.250.194'
DATABASE = 'analisis_pro'
USERNAME = 'david'
PASSWORD = 'Davidsql2024*'

def get_table_schema(table_name):
    """Get the exact column structure of a SQL Server table"""
    try:
        # Connection string
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        conn = pyodbc.connect(conn_str)
        
        # Get column information
        query = f"""
        SELECT 
            COLUMN_NAME,
            ORDINAL_POSITION,
            DATA_TYPE,
            IS_NULLABLE,
            CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '{table_name}' 
        ORDER BY ORDINAL_POSITION
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"\n📊 TABLE SCHEMA: {table_name}")
        print("=" * 60)
        for _, row in df.iterrows():
            nullable = "NULL" if row['IS_NULLABLE'] == 'YES' else "NOT NULL"
            max_len = f"({row['CHARACTER_MAXIMUM_LENGTH']})" if row['CHARACTER_MAXIMUM_LENGTH'] else ""
            print(f"{row['ORDINAL_POSITION']:2d}. {row['COLUMN_NAME']:<25} {row['DATA_TYPE']}{max_len} {nullable}")
        
        return df['COLUMN_NAME'].tolist()
        
    except Exception as e:
        print(f"❌ Error getting schema for {table_name}: {e}")
        return []

def check_csv_structure(csv_file):
    """Check the structure of our generated CSV"""
    try:
        df = pd.read_csv(csv_file, sep=';', nrows=1)
        print(f"\n📄 CSV STRUCTURE: {csv_file}")
        print("=" * 60)
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        return df.columns.tolist()
    except Exception as e:
        print(f"❌ Error reading CSV {csv_file}: {e}")
        return []

if __name__ == "__main__":
    print("🔍 CHECKING TABLE SCHEMAS AND CSV STRUCTURES")
    print("=" * 80)
    
    # Check MAT table
    mat_sql_cols = get_table_schema('MAT_07_24')
    mat_csv_cols = check_csv_structure('MAT_2025.csv')
    
    print(f"\n🔍 MAT COLUMN COMPARISON:")
    print("=" * 40)
    print(f"SQL Table columns: {len(mat_sql_cols)}")
    print(f"CSV columns: {len(mat_csv_cols)}")
    
    if mat_sql_cols and mat_csv_cols:
        print("\nColumn Mapping:")
        max_len = max(len(mat_sql_cols), len(mat_csv_cols))
        for i in range(max_len):
            sql_col = mat_sql_cols[i] if i < len(mat_sql_cols) else "MISSING"
            csv_col = mat_csv_cols[i] if i < len(mat_csv_cols) else "MISSING"
            match = "✓" if sql_col == csv_col else "✗"
            print(f"{i+1:2d}. {match} SQL: {sql_col:<25} CSV: {csv_col}")
    
    print("\n" + "=" * 80)
    
    # Check BI_NOTAS table
    notas_sql_cols = get_table_schema('BI_NOTAS_07012025_STG_Ucampus')
    notas_csv_cols = check_csv_structure('BI_NOTAS_2025_STG_Ucampus.csv')
    
    print(f"\n🔍 BI_NOTAS COLUMN COMPARISON:")
    print("=" * 40)
    print(f"SQL Table columns: {len(notas_sql_cols)}")
    print(f"CSV columns: {len(notas_csv_cols)}")
    
    if notas_sql_cols and notas_csv_cols:
        print("\nColumn Mapping:")
        max_len = max(len(notas_sql_cols), len(notas_csv_cols))
        for i in range(max_len):
            sql_col = notas_sql_cols[i] if i < len(notas_sql_cols) else "MISSING"
            csv_col = notas_csv_cols[i] if i < len(notas_csv_cols) else "MISSING"
            match = "✓" if sql_col == csv_col else "✗"
            print(f"{i+1:2d}. {match} SQL: {sql_col:<25} CSV: {csv_col}")
