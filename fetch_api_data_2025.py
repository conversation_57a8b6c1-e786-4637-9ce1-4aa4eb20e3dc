#!/usr/bin/env python3
"""
Script to fetch 2025 data from the Ucampus API for tasa de aprobacion analysis.
Fetches data from the highlighted endpoints in the API diagram.
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
import os

# API base URL and credentials
BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
USERNAME = '48bc01ad5501e88f249831516829efeb'
PASSWORD = '030643a80361e103'

# Endpoints to fetch (highlighted in green in the diagram)
# Note: Some endpoints like 'avance_curricular' and 'malla' require specific parameters
# and will be skipped in the main fetch
ENDPOINTS = [
    "instituciones",
    "carreras",
    "planes",
    "carreras_alumnos",
    "ramos",
    "cursos",
    "cursos_inscritos",
    "periodos",  # Added to get period information
    "situaciones",  # Added for academic situations
    # "personas",  # Commented out as it's very large without filters
    # "malla",  # Requires id_plan parameter
    # "avance_curricular"  # Requires id_plan and rut parameters
]

def get_endpoint_params(endpoint, year=2025):
    """
    Get the appropriate parameters for each endpoint based on API documentation.

    Args:
        endpoint (str): The API endpoint name
        year (int): Year to filter data (default: 2025)

    Returns:
        list: List of parameter combinations to try
    """
    # Endpoints that require specific parameters (based on API docs)
    endpoint_params = {
        'avance_curricular': [
            # This endpoint requires id_plan and rut, skip for now
            {'skip': True}
        ],
        'carreras': [
            {'vigente': 1},
            {}
        ],
        'carreras_alumnos': [
            {},  # No year filter, gets all data
        ],
        'planes': [
            {},
        ],
        'malla': [
            # This endpoint requires id_plan, skip for now
            {'skip': True}
        ],
        'cursos': [
            {'periodo': f'{year}.1'},
            {'periodo': f'{year}.2'},
            {'periodo': f'{year}.0'},  # Summer period
            {'periodo': f'{year}.1,{year}.2'},  # Both semesters
        ],
        'personas': [
            {},  # No year filter available
        ],
        'cursos_inscritos': [
            {'periodo': f'{year}.1'},
            {'periodo': f'{year}.2'},
            {'periodo': f'{year}.0'},
            {'periodo': f'{year}.1,{year}.2'},
        ],
        'ramos': [
            {},
        ],
        'instituciones': [
            {},
        ],
        'periodos': [
            {'activo': 1},  # Get active periods
            {},
        ],
        'situaciones': [
            {'periodo': f'{year}.1'},
            {'periodo': f'{year}.2'},
            {'periodo': f'{year}.0'},
            {},
        ]
    }

    return endpoint_params.get(endpoint, [{}])

def fetch_endpoint_data(endpoint, year=2025, params=None):
    """
    Fetch data from a specific API endpoint for the given year.

    Args:
        endpoint (str): The API endpoint name
        year (int): Year to filter data (default: 2025)
        params (dict): Additional parameters for the API call

    Returns:
        dict: JSON response from the API or None if error
    """
    url = f"{BASE_URL}/{endpoint}"

    # Get endpoint-specific parameters
    if params:
        param_combinations = [params]
    else:
        param_combinations = get_endpoint_params(endpoint, year)

    # Skip endpoints that require specific parameters we don't have
    if param_combinations and param_combinations[0].get('skip'):
        print(f"⚠ Skipping {endpoint} - requires specific parameters (id_plan, rut, etc.)")
        return None

    for i, test_params in enumerate(param_combinations):
        try:
            print(f"Fetching data from {endpoint}... (attempt {i+1}) - params: {test_params}")
            response = requests.get(url, auth=(USERNAME, PASSWORD), params=test_params, timeout=30)
            response.raise_for_status()

            # Try to parse as JSON
            data = response.json()

            # Filter for 2025 data if we got data without year filter
            if not any(key in test_params for key in ['periodo', 'year', 'anio', 'año', 'annio']):
                data = filter_2025_data(data, year)

            record_count = len(data) if isinstance(data, list) else 1
            print(f"✓ Successfully fetched {record_count} records from {endpoint}")
            return data

        except requests.exceptions.RequestException as e:
            print(f"  Request error (attempt {i+1}): {e}")
            if i == len(param_combinations) - 1:  # Last attempt
                print(f"  Response status: {getattr(e.response, 'status_code', 'N/A')}")
                print(f"  Response text: {getattr(e.response, 'text', 'N/A')[:200]}...")
            continue
        except json.JSONDecodeError as e:
            print(f"  JSON parse error (attempt {i+1}): {e}")
            continue
        except Exception as e:
            print(f"  Unexpected error (attempt {i+1}): {e}")
            continue

    print(f"✗ Failed to fetch data from {endpoint} after all attempts")
    return None

def filter_2025_data(data, year=2025):
    """
    Filter data for the specified year if the API doesn't support year filtering.

    Args:
        data: Raw data from API
        year (int): Year to filter for

    Returns:
        Filtered data
    """
    if not isinstance(data, list):
        return data

    # Common year field names to check
    year_fields = ['year', 'anio', 'año', 'annio', 'fecha', 'periodo']

    filtered_data = []
    for record in data:
        if isinstance(record, dict):
            for field in year_fields:
                if field in record:
                    try:
                        record_year = int(str(record[field])[:4])  # Extract year from date/string
                        if record_year == year:
                            filtered_data.append(record)
                            break
                    except (ValueError, TypeError):
                        continue
        else:
            # If not a dict, keep the record
            filtered_data.append(record)

    return filtered_data if filtered_data else data

def save_data_to_csv(data, endpoint, year=2025):
    """
    Save fetched data to CSV file.
    
    Args:
        data: Data to save (list of dicts or dict)
        endpoint (str): Endpoint name for filename
        year (int): Year for filename
    """
    if not data:
        print(f"No data to save for {endpoint}")
        return
    
    filename = f"{endpoint}_{year}.csv"
    
    try:
        # Convert to DataFrame
        if isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = pd.DataFrame([data])
        
        # Save to CSV
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✓ Saved {len(df)} records to {filename}")
        
    except Exception as e:
        print(f"✗ Error saving {endpoint} data to CSV: {e}")

def main():
    """
    Main function to fetch all endpoint data for 2025.
    """
    print("=" * 60)
    print("UCAMPUS API DATA FETCHER - 2025 DATA")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Create output directory if it doesn't exist
    output_dir = "api_data_2025"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")
    
    os.chdir(output_dir)
    
    successful_fetches = 0
    failed_fetches = 0
    
    for endpoint in ENDPOINTS:
        print(f"\n--- Processing {endpoint} ---")
        
        # Fetch data
        data = fetch_endpoint_data(endpoint, year=2025)
        
        if data:
            # Save to CSV
            save_data_to_csv(data, endpoint, year=2025)
            successful_fetches += 1
        else:
            failed_fetches += 1
        
        # Add small delay between requests to be respectful to the API
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Successful fetches: {successful_fetches}")
    print(f"Failed fetches: {failed_fetches}")
    print(f"Total endpoints: {len(ENDPOINTS)}")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if successful_fetches > 0:
        print(f"\nData files saved in: {os.path.abspath('.')}")
        print("\nGenerated files:")
        for file in os.listdir('.'):
            if file.endswith('.csv'):
                print(f"  - {file}")

if __name__ == "__main__":
    main()
