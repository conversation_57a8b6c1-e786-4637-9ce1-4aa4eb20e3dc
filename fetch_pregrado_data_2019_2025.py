#!/usr/bin/env python3
"""
Fetch API data from 2019-2025 in big batches, filtering for PREGRADO only.
Excludes masters, diplomas, PhDs. Creates tables with date suffix.
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
import os

# API credentials
API_BASE_URL = "https://ucampus.uantof.cl/api/0/mufasa"
API_USERNAME = '48bc01ad5501e88f249831516829efeb'
API_PASSWORD = '030643a80361e103'

# Years to fetch
YEARS = list(range(2019, 2026))  # 2019-2025
CURRENT_DATE = datetime.now().strftime("%d%m%Y")

# API call counter
api_calls_made = 0
MAX_API_CALLS = 1000

def log_api_call():
    """Track API calls to stay under limit"""
    global api_calls_made
    api_calls_made += 1
    print(f"API calls made: {api_calls_made}/{MAX_API_CALLS}")
    if api_calls_made >= MAX_API_CALLS:
        print("⚠️ WARNING: Approaching API call limit!")

def fetch_endpoint_batch(endpoint, years, params_template=None):
    """Fetch data from endpoint for multiple years in batch"""
    print(f"\n🔄 Fetching {endpoint} data for years {years[0]}-{years[-1]}...")
    
    all_data = []
    
    for year in years:
        if api_calls_made >= MAX_API_CALLS:
            print(f"❌ Stopping - reached API call limit")
            break
            
        try:
            url = f"{API_BASE_URL}/{endpoint}"
            
            # Prepare parameters based on endpoint
            if endpoint == 'cursos_inscritos':
                # Get both semesters for each year
                params = {'periodo': f'{year}.1,{year}.2,{year}.0'}  # Include summer
            elif endpoint == 'cursos':
                params = {'periodo': f'{year}.1,{year}.2,{year}.0'}
            elif endpoint == 'situaciones':
                params = {'periodo': f'{year}.1,{year}.2,{year}.0'}
            else:
                params = params_template or {}
            
            print(f"  📡 Fetching {endpoint} for {year}... (params: {params})")
            
            response = requests.get(url, auth=(API_USERNAME, API_PASSWORD), params=params, timeout=60)
            response.raise_for_status()
            log_api_call()
            
            data = response.json()
            
            # Add year info if not present
            if isinstance(data, list):
                for record in data:
                    if 'year_fetched' not in record:
                        record['year_fetched'] = year
                        
                all_data.extend(data)
                print(f"    ✓ Got {len(data)} records for {year}")
            else:
                print(f"    ⚠️ Unexpected data format for {year}")
            
            # Small delay to be nice to the API
            time.sleep(0.5)
            
        except Exception as e:
            print(f"    ❌ Error fetching {endpoint} for {year}: {e}")
            continue
    
    print(f"✅ Total {endpoint} records: {len(all_data)}")
    return all_data

def filter_pregrado_only(data, data_type):
    """Filter data to include only pregrado programs by tipo_titulo"""
    print(f"\n🎓 Filtering {data_type} for PREGRADO only...")

    if not data:
        return data

    original_count = len(data)

    # Pregrado tipo_titulo values
    pregrado_tipos = [2, 9, 10, 11]  # Título Profesional, Bachiller, Técnico de Nivel Superior, Técnico

    filtered_data = []

    for record in data:
        if data_type == 'carreras':
            tipo_titulo = record.get('tipo_titulo')
            if tipo_titulo in pregrado_tipos:
                filtered_data.append(record)
        elif data_type == 'planes':
            # For planes, we'll need to cross-reference with carreras later
            # For now, include all
            filtered_data.append(record)
        else:
            # For other data types, include all
            filtered_data.append(record)

    filtered_count = len(filtered_data)
    excluded_count = original_count - filtered_count

    print(f"  📊 Original records: {original_count}")
    print(f"  ✅ Pregrado records: {filtered_count}")
    print(f"  ❌ Excluded (non-pregrado): {excluded_count}")

    if data_type == 'carreras':
        # Show breakdown by tipo_titulo
        tipo_counts = {}
        for record in filtered_data:
            tipo = record.get('tipo_titulo')
            tipo_texto = record.get('tipo_titulo_texto', f'Tipo {tipo}')
            tipo_counts[tipo_texto] = tipo_counts.get(tipo_texto, 0) + 1

        print(f"  📋 Breakdown by tipo_titulo:")
        for tipo_texto, count in tipo_counts.items():
            print(f"    - {tipo_texto}: {count}")

    return filtered_data

def check_for_derecho(carreras_data):
    """Check if 'derecho' (law) programs are in the data"""
    print(f"\n⚖️ Checking for DERECHO (Law) programs...")
    
    derecho_programs = []
    
    for record in carreras_data:
        nombre = record.get('nombre', '').lower()
        titulo = record.get('titulo', '').lower()
        
        if 'derecho' in nombre or 'derecho' in titulo or 'law' in nombre:
            derecho_programs.append({
                'id': record.get('id_carrera'),
                'nombre': record.get('nombre'),
                'titulo': record.get('titulo'),
                'facultad': record.get('facultad')
            })
    
    if derecho_programs:
        print(f"✅ Found {len(derecho_programs)} DERECHO programs:")
        for prog in derecho_programs:
            print(f"  - ID: {prog['id']}, Name: {prog['nombre']}")
            print(f"    Title: {prog['titulo']}")
            print(f"    Faculty: {prog['facultad']}")
    else:
        print("❌ No DERECHO programs found!")
    
    return derecho_programs

def save_data_to_csv(data, filename):
    """Save data to CSV file"""
    if not data:
        print(f"⚠️ No data to save for {filename}")
        return
    
    try:
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"💾 Saved {len(data)} records to {filename}")
    except Exception as e:
        print(f"❌ Error saving {filename}: {e}")

def main():
    """Main function to fetch all pregrado data 2019-2025"""
    print("🚀 FETCHING PREGRADO DATA 2019-2025")
    print("="*60)
    print(f"📅 Date suffix: {CURRENT_DATE}")
    print(f"🎯 Target: PREGRADO only (no masters/diplomas/PhDs)")
    print(f"📊 API call limit: {MAX_API_CALLS}")
    print("="*60)
    
    # Create output directory
    output_dir = f"api_data_pregrado_{CURRENT_DATE}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Fetch reference data (once for all years)
    print("\n📋 FETCHING REFERENCE DATA...")
    
    # Instituciones
    print("\n🏛️ Fetching instituciones...")
    try:
        response = requests.get(f"{API_BASE_URL}/instituciones", auth=(API_USERNAME, API_PASSWORD), timeout=30)
        response.raise_for_status()
        log_api_call()
        instituciones = response.json()
        save_data_to_csv(instituciones, f"{output_dir}/instituciones_{CURRENT_DATE}.csv")
    except Exception as e:
        print(f"❌ Error fetching instituciones: {e}")
        instituciones = []
    
    # Carreras
    print("\n🎓 Fetching carreras...")
    try:
        response = requests.get(f"{API_BASE_URL}/carreras", auth=(API_USERNAME, API_PASSWORD), timeout=30)
        response.raise_for_status()
        log_api_call()
        carreras = response.json()

        # Filter for pregrado only
        carreras = filter_pregrado_only(carreras, 'carreras')

        # Check for derecho programs
        derecho_programs = check_for_derecho(carreras)

        save_data_to_csv(carreras, f"{output_dir}/carreras_pregrado_{CURRENT_DATE}.csv")
    except Exception as e:
        print(f"❌ Error fetching carreras: {e}")
        carreras = []

    # Carreras_alumnos (important for student-career relationships)
    print("\n👥 Fetching carreras_alumnos...")
    try:
        response = requests.get(f"{API_BASE_URL}/carreras_alumnos", auth=(API_USERNAME, API_PASSWORD), timeout=60)
        response.raise_for_status()
        log_api_call()
        carreras_alumnos = response.json()
        save_data_to_csv(carreras_alumnos, f"{output_dir}/carreras_alumnos_{CURRENT_DATE}.csv")
    except Exception as e:
        print(f"❌ Error fetching carreras_alumnos: {e}")
        carreras_alumnos = []
    
    # Planes
    print("\n📚 Fetching planes...")
    try:
        response = requests.get(f"{API_BASE_URL}/planes", auth=(API_USERNAME, API_PASSWORD), timeout=30)
        response.raise_for_status()
        log_api_call()
        planes = response.json()
        
        # Filter for pregrado only
        planes = filter_pregrado_only(planes, 'planes')
        
        save_data_to_csv(planes, f"{output_dir}/planes_pregrado_{CURRENT_DATE}.csv")
    except Exception as e:
        print(f"❌ Error fetching planes: {e}")
        planes = []
    
    # Ramos
    print("\n📖 Fetching ramos...")
    try:
        response = requests.get(f"{API_BASE_URL}/ramos", auth=(API_USERNAME, API_PASSWORD), timeout=30)
        response.raise_for_status()
        log_api_call()
        ramos = response.json()
        save_data_to_csv(ramos, f"{output_dir}/ramos_{CURRENT_DATE}.csv")
    except Exception as e:
        print(f"❌ Error fetching ramos: {e}")
        ramos = []
    
    print(f"\n⚠️ READY FOR YEAR-BY-YEAR DATA FETCHING")
    print(f"API calls used so far: {api_calls_made}/{MAX_API_CALLS}")
    print(f"Remaining calls: {MAX_API_CALLS - api_calls_made}")
    
    # 2. Fetch year-specific data
    print("\n📅 FETCHING YEAR-SPECIFIC DATA...")
    
    # Cursos inscritos (most important for grades)
    cursos_inscritos = fetch_endpoint_batch('cursos_inscritos', YEARS)
    save_data_to_csv(cursos_inscritos, f"{output_dir}/cursos_inscritos_2019_2025_{CURRENT_DATE}.csv")
    
    # Cursos
    if api_calls_made < MAX_API_CALLS - 10:  # Leave some buffer
        cursos = fetch_endpoint_batch('cursos', YEARS)
        save_data_to_csv(cursos, f"{output_dir}/cursos_2019_2025_{CURRENT_DATE}.csv")
    
    # Situaciones
    if api_calls_made < MAX_API_CALLS - 10:
        situaciones = fetch_endpoint_batch('situaciones', YEARS)
        save_data_to_csv(situaciones, f"{output_dir}/situaciones_2019_2025_{CURRENT_DATE}.csv")
    
    print(f"\n✅ DATA FETCHING COMPLETE!")
    print(f"📊 Total API calls made: {api_calls_made}/{MAX_API_CALLS}")
    print(f"📁 Data saved to: {output_dir}/")
    print(f"\n⚠️ NEXT STEP: Turn on VPN before running database import!")

if __name__ == "__main__":
    main()
